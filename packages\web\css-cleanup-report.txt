=== CSS样式清理分析报告 ===

总计发现 511 个可能未使用的CSS选择器

🟢 【建议删除】自定义类名 (相对安全):
共 241 个

📁 src\renderer\components\apidoc\mock\g-mock.vue:
   - .bar (.bar)
   - .preview (.preview)

📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue:
   - .valid-input, .ipt-wrap, .ipt-inner (.valid-input .ipt-wrap .ipt-inner)
   - .file-error (.file-error)
   - .no-border (&.no-border)
   - .mode-list (.mode-list)

📁 src\renderer\components\apidoc\params-view\g-params-view.vue:
   - .error (&.error)
   - .indent (.indent)
   - .path (.path)
   - .description (.description)
   - .colon (.colon)
   - .colon (.colon)
   - .bracket (.bracket)
   - .comma (.comma)
   - .curly-brace (.curly-brace)
   - .string-value (.string-value)
   - .boolean-value (.boolean-value)
   - .number-value (.number-value)
   - .null-value (.null-value)
   - .file-value (.file-value)

📁 src\renderer\components\common\collapse-card\g-collapse-card.vue:
   - .tail (.tail)
   - .content (// 内容区域
  .content)

📁 src\renderer\components\common\ellipsis-content\g-ellipsis-content.vue:
   - .copy (.copy)

📁 src\renderer\components\common\sse-view\components\popover\sse-popover.vue:
   - .raw-block (.raw-block)
   - .raw-block (.raw-block)

📁 src\renderer\components\common\sse-view\g-sse-view.vue:
   - .action-icons (.action-icons)
   - .icon (.icon)
   - .error, .no-result (font-size: 12px;
          padding: 8px 12px;
          margin: 0;

          &:not(.error):not(.no-result))
   - .no-result (&.no-result)
   - .error (&.error)
   - .error, .no-result (font-size: 12px;

          &:not(.error):not(.no-result))
   - .no-result (&.no-result)
   - .error (&.error)
   - .loading-icon (#909399);
    font-size: 14px;
    gap: 12px;

    .loading-icon)
   - .raw-data (#ebeef5);
    border-radius: 4px;
    margin: 0 12px 12px 12px;

    .raw-data)
   - .sse-message-hex (#ffffff);

      &.sse-message-hex)
   - .message-index (.message-index)
   - .message-content (.message-content)
   - .message-timestamp (.message-timestamp)
   - .sse-message-hex (#f2f6fc);
        cursor: pointer;

        &.sse-message-hex)
   - .highlight (// 高亮样式
:deep(.highlight))

📁 src\renderer\components\common\websocket-view\components\filter\websocket-filter.vue:
   - .message-type-filter (display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 35px;
      flex: 0 0 40%;
      margin-left: auto;
      .message-type-filter)
   - .download-icon (.download-icon:hover)
   - .clear-icon (.clear-icon:hover)
   - .error, .no-result (font-size: 12px;
        padding: 8px 12px;
        margin: 0;

        &:not(.error):not(.no-result))
   - .no-result (&.no-result)
   - .error (&.error)

📁 src\renderer\components\common\websocket-view\components\popover\websocket-popover.vue:
   - .type-send (padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;

          &.type-send)
   - .type-receive (&.type-receive)
   - .type-connected (&.type-connected)
   - .type-disconnected (&.type-disconnected)
   - .type-error (&.type-error)
   - .type-heartbeat (&.type-heartbeat)
   - .type-startConnect (&.type-startConnect)
   - .type-reconnecting (&.type-reconnecting)

📁 src\renderer\components\common\websocket-view\g-websocket-view.vue:
   - .type-send (border-radius: 3px;
        font-size: 14px;
        min-width: 20px;
        text-align: center;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.type-send)
   - .type-heartbeat (&.type-heartbeat)
   - .type-receive (&.type-receive)

📁 src\renderer\pages\header\header.vue:
   - .add-tab-btn (.add-tab-btn)
   - .add-tab-btn (.add-tab-btn:focus)
   - .add-tab-btn (.add-tab-btn:hover)
   - .right (.right)
   - .navigation-control (.navigation-control)
   - .navigation-control (.navigation-control i)
   - .navigation-control (.navigation-control i:hover)
   - .language-btn (.language-btn)
   - .language-btn (.language-btn:hover)
   - .language-text (.language-text)
   - .window-control (.window-control)
   - .window-control (.window-control i)
   - .window-control (.window-control i:hover)

📁 src\renderer\pages\layout\header.vue:
   - .close (&.close:hover)
   - .process (.process)

📁 src\renderer\pages\login\login.vue:
   - .item-wrap (.item-wrap)

📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue:
   - .ws-icon (.ws-icon)
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top)
   - .node-bottom (.node-bottom)
   - .error (flex: 0 0 75%;
      height: 22px;
      border: 1px solid var(--theme-color);
      font-size: 1em;
      margin-left: -1px;

      &.error)
   - .folder-icon (.folder-icon)
   - .folder-icon (.folder-icon)
   - .hot-key (.hot-key)

📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue:
   - .item (position: relative;
    align-items: center;
    display: flex;

    .item)
   - .operation (.operation)
   - .more (.more)
   - .label (height: 40px;
  width: 100%;
  padding: 0 10px 0 20px;
  display: flex;
  align-items: center;

  // cursor: default;
  .label)
   - .shortcut (.shortcut)
   - .svg-icon (.svg-icon)
   - .project-wrap (.project-wrap)
   - .item-title (height: 35px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item-title)
   - .item-content (background-color: var(--theme-color);
      color: var(--white);
      cursor: pointer;

      .item-content)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\dialog\curd-host\curd-host.vue:
   - .url-wrap (.url-wrap)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\operation\operation.vue:
   - .prefix (position: sticky;
  top: 0;
  padding: 10px 20px;
  box-shadow: 0 3px 2px var(--gray-400);
  background: var(--white);
  z-index: var(--zIndex-request-info-wrap);
  height: var(--apiflow-apidoc-operation-height);

  &.prefix)
   - .proxy-wrap (.proxy-wrap)
   - .tip (.tip)
   - .env-item (.env-item)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\body\body.vue:
   - .operation (.operation)
   - .head (.head)
   - .tail (.tail)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\headers\headers.vue:
   - .value-wrap (.value-wrap)
   - .folder-icon (.folder-icon)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\mock\components\mock-response\mock-response.vue:
   - .mock-json-editor (height: calc(100vh - 610px);
    min-height: 200px;
    border: 1px solid var(--gray-500);
    display: flex;
    position: relative;

    .mock-json-editor)
   - .tip (.tip)
   - .format-btn (.format-btn)
   - .raw-editor-wrap (.raw-editor-wrap)
   - .img-wrap (.img-wrap)
   - .image-demo (.image-demo)
   - .svg-icon (.svg-icon)
   - .img (.img)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\params.vue:
   - .workbench (.workbench)
   - .is-fixed, .is-dot (transition: none;
    top: 10px;

    &.is-fixed.is-dot)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\response.vue:
   - .type-text (max-width: 200px;

      .type-text)
   - .editor (.editor)
   - .format-btn (.format-btn)
   - .header (.header)
   - .tail (.tail)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\base-info\base-info.vue:
   - .svg-icon (flex-grow: 0;
  flex-shrink: 0;
  box-shadow: 0 3px 2px var(--gray-400);
  margin-bottom: 10px;
  padding: 10px;
  height: var(--apiflow-apidoc-request-view-height);
  overflow: hidden;

  .svg-icon)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\body\body.vue:
   - .apply-response (.apply-response)
   - .text-tool (height: 100%;
    .text-tool)
   - .operation (.operation)
   - .process (.process)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\request\request.vue:
   - .body-wrap (.body-wrap)
   - .download (margin-left: 25px;
    .download)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\res-info\res-info.vue:
   - .content-type (overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  .content-type)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\view\view.vue:
   - .url (.url)
   - .view-block (.view-block)
   - .title (.title)
   - .remark (.remark)
   - .api-doc-subtitle (.api-doc-subtitle)
   - .api-doc-method (.api-doc-method)
   - .api-doc-url (.api-doc-url)
   - .api-doc-description (.api-doc-description)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\apiflow\components\node\node.vue:
   - .create-line-dot (.create-line-dot)
   - .resize-border (.resize-border)
   - .position-info (.position-info)
   - .title (0.12);
        .title)
   - .method (height: 30px;
        display: flex;
        align-items: center;
        font-size: 13px;
        padding: 0 10px;
        width: 100%;
        .method)
   - .url (.url)
   - .content (.content)
   - .empty (.empty)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\config\config.vue:
   - .item-text (height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      font-size: 12px;
      flex: 0 0 auto;
      width: 200px;
      cursor: default;
      padding: 0 10px;
      .item-text)
   - .iconfont (.iconfont)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\cookies\cookies.vue:
   - .expire-tip (margin: 0 auto;
  padding: 16px 0;
  font-size: 22px;
  .expire-tip)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\export\export.vue:
   - .more (.more)
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
                flex-direction: column;
                flex: 1;
                overflow: hidden;
                .node-top)
   - .node-bottom (.node-bottom)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\export\fork\fork.vue:
   - .right (.right)
   - .more (.more)
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;

        .node-top)
   - .node-bottom (.node-bottom)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\import\import.vue:
   - .more (.more)
   - .file-icon (.file-icon)
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top)
   - .node-bottom (.node-bottom)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\add.vue:
   - .link-icon (.link-icon)
   - .more (.more)
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .node-top)
   - .node-bottom (.node-bottom)
   - .error (flex: 0 0 75%;
      height: 22px;
      border: 1px solid #409EFF;
      font-size: 1em;
      margin-left: -1px;
      &.error)
   - .more (.more)
   - .select-node (&.select-node)
   - .folder-icon (.folder-icon)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\edit.vue:
   - .link-icon (.link-icon)
   - .more (.more)
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .node-top)
   - .node-bottom (.node-bottom)
   - .error (flex: 0 0 75%;
      height: 22px;
      border: 1px solid #409EFF;
      font-size: 1em;
      margin-left: -1px;
      &.error)
   - .more (.more)
   - .select-node (&.select-node)
   - .folder-icon (.folder-icon)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\link\link.vue:
   - .content-area (// 内容区域
  .content-area)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\package\package.vue:
   - .drag-wrap (display: flex;
    height: calc(100vh - 100px);
    padding: 20px;
    overflow-y: auto;
    .drag-wrap)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\recycler\recycler.vue:
   - .desc (.desc)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\variable\variable.vue:
   - .file-notice (flex: 0 0 500px;
    margin-right: 10px;
    .file-notice)
   - .right (.right)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\operation\operation.vue:
   - .action-buttons (.action-buttons)
   - .status-tag (.status-tag)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\headers\headers.vue:
   - .value-wrap (.value-wrap)
   - .folder-icon (.folder-icon)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\message\message.vue:
   - .action-options (.action-options)
   - .interval-unit (display: flex;
          align-items: center;
          margin-left: 24px;

          .interval-unit)
   - .config-label (margin-bottom: 16px;

    .config-label)
   - .interval-unit (display: flex;
      align-items: center;
      gap: 8px;

      .interval-unit)
   - .heartbeat-content-input (.heartbeat-content-input)
   - .config-actions (.config-actions)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\params.vue:
   - .workbench (.workbench)

📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\pre-script\pre-script.vue:
   - .script-actions (.script-actions)

📁 src\renderer\pages\modules\apidoc\doc-edit\dialog\save-doc\save-doc.vue:
   - .more (.more)
   - .file-icon (.file-icon)
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top)
   - .node-bottom (.node-bottom)

📁 src\renderer\pages\modules\apidoc\doc-edit\nav\nav.vue:
   - .unfixed (display: inline-block;
        width: 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        // font-size: fz(12);
        &.unfixed)
   - .iconfont (.iconfont)
   - .has-change (position: absolute;
      right: 0;
      width: 25px;
      height: 100%;
      cursor: pointer;

      &:hover>.has-change)
   - .close (&:hover>.close)
   - .dot (-50%);
        width: 20px;
        height: 20px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        z-index: 2;

        .dot)

📁 src\renderer\pages\modules\apidoc\doc-list\tab-a\tab-a.vue:
   - .advance-icon (.advance-icon)
   - .project-bottom (.project-bottom)

📁 src\renderer\pages\modules\apidoc\doc-list\tab-b\tab-b.vue:
   - .user-list (.user-list)

📁 src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue:
   - .folder-icon (.folder-icon)
   - .node-top (display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top)
   - .node-bottom (.node-bottom)
   - .select-node (&.select-node)
   - .folder-icon (.folder-icon)

📁 src\renderer\pages\modules\apidoc\doc-share\content\content.vue:
   - .content-format-label (.content-format-label)
   - .api-doc-subtitle (.api-doc-subtitle)
   - .api-doc-empty (.api-doc-empty)
   - .api-doc-response-title (display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
    
    .api-doc-response-title)
   - .status-code, .success (.status-code.success)
   - .type-label (.type-label)
   - .api-doc-raw-body (// 原始数据样式
.api-doc-raw-body)
   - .api-doc-response-meta (.api-doc-response-meta)

📁 src\renderer\pages\modules\apidoc\doc-share\nav\nav.vue:
   - .unfixed (display: inline-block;
        width: 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.unfixed)

📁 src\renderer\pages\modules\permission\menu\menu.vue:
   - .label (width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    overflow: hidden;

    .label)
   - .contextmenu (.contextmenu)

📁 src\renderer\pages\modules\permission\role\add\components\client-menus.vue:
   - .node-name (display: flex;
      align-items: center;
      height: 30px;
      width: 100%;

      .node-name)

📁 src\renderer\pages\modules\permission\role\edit\components\client-menus.vue:
   - .node-name (display: flex;
      align-items: center;
      height: 30px;
      width: 100%;

      .node-name)

📁 src\renderer\pages\modules\user-center\cacheManager\components\DataBackup.vue:
   - .info-text (display: inline-block;
    padding: 8px 12px;
    background: var(--bg-light);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    
    .info-text)
   - .path-error-message (.path-error-message)
   - .progress-info (.progress-info)

📁 src\renderer\pages\modules\user-center\cacheManager\components\IndexedDBDetail.vue:
   - .empty-text (0.1);
    padding: 40px 20px;
    text-align: center;

    .empty-text)

📁 src\renderer\pages\modules\user-center\cacheManager\components\LocalStorageDetail.vue:
   - .empty-text (0.1);
    padding: 40px 20px;
    text-align: center;

    .empty-text)

📁 src\renderer\pages\modules\user-center\cacheManager\dialog\indexedDBDialog.vue:
   - .value-content (.value-content)
   - .value-preview (.value-preview)
   - .value-preview, .clickable (.value-preview.clickable)
   - .value-preview, .clickable (.value-preview.clickable:hover)
   - .json-popover-content (.json-popover-content)
   - .popover-header (.popover-header)
   - .popover-title (.popover-title)
   - .json-editor-container (.json-editor-container)
   - .pagination-container (.pagination-container)
   - .empty-detail (.empty-detail)
   - .empty-text (.empty-text)

📁 src\renderer\pages\modules\user-center\UserCenter.vue:
   - .fade-enter-from (.fade-enter-from)
   - .fade-leave-to (.fade-leave-to)

🟡 【需要检查】ID选择器:
共 36 个

📁 src\renderer\App.vue: #app
📁 src\renderer\components\apidoc\virtual-scroll\g-virtual-scroll.vue: #dcdfe6
📁 src\renderer\components\common\sse-view\components\popover\sse-popover.vue: #ebeef5
📁 src\renderer\components\common\sse-view\g-sse-view.vue: #ffffff
📁 src\renderer\components\common\sse-view\g-sse-view.vue: #f5f7fa
📁 src\renderer\components\common\sse-view\g-sse-view.vue: #e4e7ed
📁 src\renderer\components\common\sse-view\g-sse-view.vue: #ffffff
📁 src\renderer\components\common\sse-view\g-sse-view.vue: #fafcff
📁 src\renderer\components\common\websocket-view\components\filter\websocket-filter.vue: #ffffff
📁 src\renderer\components\common\websocket-view\components\popover\websocket-popover.vue: #ebeef5
📁 src\renderer\components\common\websocket-view\g-websocket-view.vue: #f56c6c
📁 src\renderer\components\common\websocket-view\g-websocket-view.vue: #fef0f0
📁 src\renderer\components\common\websocket-view\g-websocket-view.vue: #f0f9ff
📁 src\renderer\components\common\websocket-view\g-websocket-view.vue: #e6a23c
📁 src\renderer\components\common\websocket-view\g-websocket-view.vue: #fdf6ec
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: #adb5bd
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: #f56c6c
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\operation\operation.vue: #d1d5da, #f0f0f0
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\children\mime.vue: #f56c6c
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\children\status.vue: #f56c6c
📁 src\renderer\pages\modules\apidoc\doc-edit\content\recycler\components\doc-detail.vue: #f56c6c
📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\operation\operation.vue: #d1d5da, #f0f0f0
📁 src\renderer\pages\modules\user-center\cacheManager\CacheManagement.vue: #fff, #eaeaea
📁 src\renderer\pages\modules\user-center\cacheManager\components\IndexedDBDetail.vue: #fff
📁 src\renderer\pages\modules\user-center\cacheManager\components\LocalStorageDetail.vue: #fff
📁 src\renderer\pages\modules\user-center\componentLibrary\ComponentLibrary.vue: #dcdfe6
📁 src\renderer\pages\modules\user-center\componentLibrary\ComponentLibrary.vue: #ebeef5, #fff
📁 src\renderer\pages\modules\user-center\componentLibrary\ComponentLibrary.vue: #f5f7fa, #dcdfe6
📁 src\renderer\pages\modules\user-center\componentLibrary\components\Button.vue: #fff, #dcdfe6
📁 src\renderer\pages\modules\user-center\componentLibrary\components\Dialog.vue: #ebeef5
📁 src\renderer\pages\modules\user-center\componentLibrary\components\Dialog.vue: #fff, #dcdfe6
📁 src\renderer\pages\modules\user-center\componentLibrary\components\Input.vue: #dcdfe6
📁 src\renderer\pages\modules\user-center\userInfo\UserInfo.vue: #ccc
📁 src\renderer\pages\modules\user-center\userInfo\UserInfo.vue: #ecf5ff, #d9ecff
📁 src\renderer\pages\modules\user-center\userInfo\UserInfo.vue: #dcdfe6
📁 src\renderer\pages\modules\user-center\userInfo\UserInfo.vue: #dcdfe6

🟡 【需要手动检查】可能的动态类名:
共 21 个

📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .active (cursor: default;
    border: 1px dashed var(--gray-400);
    display: flex;
    align-items: center;
    height: 30px;
    position: relative;
    font-size: 13px;
    &.active)
📁 src\renderer\components\apidoc\params-tree\g-params-tree2.vue: .active (cursor: pointer;
        background: var(--gray-300);
        height: 25px;
        line-height: 25px;
        text-indent: 1em;
        width: 98%;
        position: relative;
        &.active)
📁 src\renderer\components\apidoc\params-view\g-params-view.vue: .active (&.active)
📁 src\renderer\components\apidoc\params-view\g-params-view.vue: .active (border: 1px solid transparent;

        &.active)
📁 src\renderer\components\apidoc\params-view\g-params-view.vue: .active (border: 1px solid transparent;

        &.active)
📁 src\renderer\components\common\card\g-card.vue: .active (position: relative;
    overflow-y: auto;

    &.active)
📁 src\renderer\components\common\collapse-card\g-collapse-card.vue: .disabled-icon (.disabled-icon)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\body\body.vue: .active (&.active)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\mock\components\mock-response\mock-response.vue: .active (width: 70px;
      height: 70px;
      padding: 10px;
      margin-right: 20px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      border: 1px solid transparent;

      &.active)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\response.vue: .active (.active)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\response.vue: .active (&.active)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\response.vue: .disabled (&.disabled)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\config\config.vue: .active (&.active)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\add.vue: .active-node (&.active-node)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\edit.vue: .active-node (&.active-node)
📁 src\renderer\pages\modules\apidoc\doc-list\tab-b\tab-b.vue: .is-active (&.is-active)
📁 src\renderer\pages\modules\apidoc\doc-share\content\content.vue: .is-active (font-size: 14px;
    padding: 8px 16px;
    height: auto;
    line-height: 1.5;
    
    &.is-active)
📁 src\renderer\pages\modules\permission\role\add\components\client-menus.vue: .bg-active (.bg-active)
📁 src\renderer\pages\modules\permission\role\edit\components\client-menus.vue: .bg-active (.bg-active)
📁 src\renderer\pages\modules\user-center\UserCenter.vue: .fade-enter-active (// Transition effects
.fade-enter-active)
📁 src\renderer\pages\modules\user-center\UserCenter.vue: .fade-leave-active (.fade-leave-active)

🔴 【谨慎删除】Element Plus相关样式:
共 78 个
这些样式可能用于覆盖Element Plus组件样式，删除前请仔细确认

📁 src\renderer\components\apidoc\mock\g-mock.vue: .el-tabs__header
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-input__wrapper
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-input__wrapper
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-input-number .el-input__inner
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-input__inner
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-select__wrapper
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-tree-node:focus>.el-tree-node__content
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-tree__drop-indicator
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: // 禁用动画提高性能
.el-collapse-transition-enter-active
📁 src\renderer\components\apidoc\params-tree\g-params-tree.vue: .el-collapse-transition-leave-active
📁 src\renderer\components\apidoc\params-tree\g-params-tree2.vue: width: 100%;
    display: flex;
    align-items: center;
    .el-input-number .el-input__inner
📁 src\renderer\components\common\forms\search\g-search.vue: .el-form-item
📁 src\renderer\components\common\sse-view\g-sse-view.vue: flex: 1;
          transition: all 0.3s ease;

          :deep(.el-input__suffix)
📁 src\renderer\components\common\sse-view\g-sse-view.vue: flex: 1;
          max-width: none;

          :deep(.el-input__suffix)
📁 src\renderer\components\common\valid-input\g-valid-input.vue: width: 100%;
    height: 29px;
    .el-textarea__inner
📁 src\renderer\pages\layout\header.vue: display: flex;
    align-items: center;
    flex: 1;
    height: 100%;

    .el-menu
📁 src\renderer\pages\login\login.vue: .el-carousel__item
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: >.el-tree-node__content
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: .el-tree__drop-indicator
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: // 禁用动画提高性能
  .el-collapse-transition-enter-active
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: .el-collapse-transition-leave-active
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: align-items: flex-start;

      &>.el-tree-node__expand-icon
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: .el-tree-node__content
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue: .el-tree-node__content>.el-tree-node__expand-icon
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: width: 25px;
    height: 25px;
    position: absolute;
    top: 8px;
    right: 25px;
    display: flex;
    align-items: center;
    justify-content: center;

    .el-badge__content
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: .el-input__wrapper
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: flex: 0 0 auto;

  .el-checkbox
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: .el-radio
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: .el-checkbox-group
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: .el-button--text
📁 src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue: .el-radio-group
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\apidoc.vue: flex-direction: column;
    overflow: hidden;

    .el-divider--horizontal
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\apidoc.vue: .el-divider--horizontal
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\operation\operation.vue: display: flex;
    margin-top: 10px;

    :deep(.el-input__inner)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\operation\operation.vue: display: flex;
      align-items: center;

      :deep(.el-select)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\operation\operation.vue: .el-input__suffix
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\body\body.vue: border-bottom: 1px solid var(--gray-300);
      display: flex;
      align-items: center;
      padding: 3px 20px 3px 5px;

      .el-input__inner
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\mock\mock.vue: .el-tabs
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\params.vue: .el-tabs
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\params.vue: .el-tabs__item
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\params.vue: .el-tabs__item
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\response.vue: .el-input__inner
📁 src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\response.vue: height: calc(100vh - var(--apiflow-apidoc-request-view-height) - var(--apiflow-doc-nav-height) - 30px);
  overflow-y: auto;
  
  :deep(.el-tabs__header)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\config\config.vue: .el-form-item
📁 src\renderer\pages\modules\apidoc\doc-edit\content\config\config.vue: .el-form-item
📁 src\renderer\pages\modules\apidoc\doc-edit\content\export\export.vue: :deep(.el-tree-node__content)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\export\export.vue: :deep(.el-tree-node__content>.el-tree-node__expand-icon)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\export\fork\fork.vue: .el-divider--horizontal
📁 src\renderer\pages\modules\apidoc\doc-edit\content\export\fork\fork.vue: .el-tree-node__content
📁 src\renderer\pages\modules\apidoc\doc-edit\content\export\fork\fork.vue: .el-tree__drop-indicator
📁 src\renderer\pages\modules\apidoc\doc-edit\content\history\history.vue: .el-button--text
📁 src\renderer\pages\modules\apidoc\doc-edit\content\import\import.vue: .el-upload-dragger
📁 src\renderer\pages\modules\apidoc\doc-edit\content\import\import.vue: .el-tree-node__content
📁 src\renderer\pages\modules\apidoc\doc-edit\content\import\import.vue: .el-tree-node__content>.el-tree-node__expand-icon
📁 src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\add.vue: :deep(.el-tree-node__content)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\edit.vue: :deep(.el-tree-node__content)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\recycler\recycler.vue: min-height: 40px;
      display: flex;
      align-items: center;

      .el-button--text
📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\operation\operation.vue: flex: 1;

      :deep(.el-input__inner)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\after-script\after-script.vue: flex: 1;
    margin-bottom: 16px;

    :deep(.el-textarea__inner)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\config\config.vue: :deep(.el-form-item__label)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\pre-script\pre-script.vue: :deep(.el-alert__content)
📁 src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\pre-script\pre-script.vue: flex: 1;
    
    :deep(.el-textarea__inner)
📁 src\renderer\pages\modules\apidoc\doc-edit\dialog\save-doc\save-doc.vue: :deep(.el-tree-node__content)
📁 src\renderer\pages\modules\apidoc\doc-edit\dialog\save-doc\save-doc.vue: :deep(.el-tree-node__content > .el-tree-node__expand-icon)
📁 src\renderer\pages\modules\apidoc\doc-edit\nav\nav.vue: //滚动条样式
  .el-scrollbar__bar
📁 src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue: .el-input__wrapper
📁 src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue: // 禁用动画提高性能
  .el-collapse-transition-enter-active
📁 src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue: .el-collapse-transition-leave-active
📁 src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue: align-items: flex-start;

      &>.el-tree-node__expand-icon
📁 src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue: .el-tree-node__content
📁 src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue: .el-tree-node__content>.el-tree-node__expand-icon
📁 src\renderer\pages\modules\apidoc\doc-share\content\content.vue: width: 100%;
  
  :deep(.el-tabs__header)
📁 src\renderer\pages\modules\apidoc\doc-share\nav\nav.vue: //滚动条样式
  .el-scrollbar__bar
📁 src\renderer\pages\modules\permission\menu\menu.vue: min-height: 70vh;

  .el-tree-node__content
📁 src\renderer\pages\modules\permission\role\add\components\client-menus.vue: min-height: 200px;
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;

    .el-tree-node__content
📁 src\renderer\pages\modules\permission\role\add\components\client-menus.vue: .el-checkbox
📁 src\renderer\pages\modules\permission\role\edit\components\client-menus.vue: min-height: 200px;
    flex: 0 0 400px;
    display: flex;
    flex-direction: column;

    .el-tree-node__content
📁 src\renderer\pages\modules\permission\role\edit\components\client-menus.vue: .el-checkbox

🔴 【不建议删除】标签选择器:
共 135 个
标签选择器通常用于全局样式，建议保留
