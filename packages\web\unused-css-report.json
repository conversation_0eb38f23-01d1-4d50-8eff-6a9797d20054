[{"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\App.vue", "unusedSelectors": [{"original": "#app", "type": "id", "names": ["app"], "fullRule": "\r\n#app {\r\n  width: 100%;\r\n  height: 100%;\r\n}"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\apidoc\\mock\\g-mock.vue", "unusedSelectors": [{"original": ".bar", "type": "class", "names": ["bar"], "fullRule": "\r\n\r\n    .bar {\r\n      height: 100%;\r\n      width: 1px;\r\n      background: var(--gray-400);\r\n    }"}, {"original": ".preview", "type": "class", "names": ["preview"], "fullRule": "\r\n\r\n    .preview {\r\n      padding: 10px;\r\n      height: 100%;\r\n      flex: 1;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      font-size: 16px;\r\n      overflow: hidden;\r\n    }"}, {"original": ".el-tabs__header", "type": "class", "names": ["el-tabs__header"], "fullRule": "\r\n\r\n  .el-tabs__header {\r\n    margin-bottom: 0;\r\n  }"}], "totalSelectors": 4}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\apidoc\\params-tree\\g-params-tree.vue", "unusedSelectors": [{"original": ".el-input__wrapper", "type": "class", "names": ["el-input__wrapper"], "fullRule": "\r\n    .el-input__wrapper {\r\n      box-shadow: none;\r\n    }"}, {"original": ".el-input__wrapper", "type": "class", "names": ["el-input__wrapper"], "fullRule": "\r\n    .el-input__wrapper {\r\n      box-shadow: 0 0 0 1px var(--el-input-border-color, var(--el-border-color)) inset;\r\n    }"}, {"original": ".el-input-number .el-input__inner", "type": "class", "names": ["el-input-number", "el-input__inner"], "fullRule": "\r\n\r\n  .el-input-number .el-input__inner {\r\n    text-align: left;\r\n  }"}, {"original": ".el-input__inner", "type": "class", "names": ["el-input__inner"], "fullRule": "\r\n\r\n  .el-input__inner {\r\n    border-radius: 0;\r\n    border: none;\r\n    border-bottom: 1px solid var(--gray-400);\r\n    font-size: fz(12);\r\n    box-shadow: none;\r\n  }"}, {"original": ".el-select__wrapper", "type": "class", "names": ["el-select__wrapper"], "fullRule": "\r\n  .el-select__wrapper {\r\n    font-size: fz(12);\r\n  }"}, {"original": ".valid-input .ipt-wrap .ipt-inner", "type": "class", "names": ["valid-input", "ipt-wrap", "ipt-inner"], "fullRule": "\r\n\r\n  .valid-input .ipt-wrap .ipt-inner {\r\n    border: none;\r\n    border-radius: 0;\r\n    border-color: var(--gray-400);\r\n    border-bottom: 1px solid var(--gray-400);\r\n    font-size: fz(12);\r\n  }"}, {"original": ".file-error", "type": "class", "names": ["file-error"], "fullRule": "\r\n  .file-error {\r\n    color: var(--red);\r\n    font-size: fz(12);\r\n  }"}, {"original": "cursor: default;\r\n    border: 1px dashed var(--gray-400);\r\n    display: flex;\r\n    align-items: center;\r\n    height: 30px;\r\n    position: relative;\r\n    font-size: 13px;\r\n    &.active", "type": "class", "names": ["active"], "fullRule": "\r\n    cursor: default;\r\n    border: 1px dashed var(--gray-400);\r\n    display: flex;\r\n    align-items: center;\r\n    height: 30px;\r\n    position: relative;\r\n    font-size: 13px;\r\n    &.active {\r\n      background: none;\r\n      border: 1px solid var(--gray-300);\r\n      cursor: auto;\r\n    }"}, {"original": "&.no-border", "type": "class", "names": ["no-border"], "fullRule": "\r\n    &.no-border {\r\n      border: none;\r\n    }"}, {"original": ".mode-list", "type": "class", "names": ["mode-list"], "fullRule": "\r\n    .mode-list {\r\n      width: 100%;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n\r\n    }"}, {"original": "cursor: pointer;\r\n      &:hover", "type": "tag", "names": ["cursor"], "fullRule": "\r\n      cursor: pointer;\r\n      &:hover {\r\n        color: var(--theme-color);\r\n      }"}, {"original": "position: absolute;\r\n        right: 3px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        font-size: 16px;\r\n        cursor: pointer;\r\n        &:hover", "type": "tag", "names": ["position"], "fullRule": "\r\n        position: absolute;\r\n        right: 3px;\r\n        top: 50%;\r\n        transform: translateY(-50%);\r\n        font-size: 16px;\r\n        cursor: pointer;\r\n        &:hover {\r\n          color: var(--red);\r\n        }"}, {"original": "flex: 0 0 20px;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      &:hover", "type": "tag", "names": ["flex"], "fullRule": "\r\n      flex: 0 0 20px;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      &:hover {\r\n        cursor: pointer;\r\n        color: var(--theme-color);\r\n      }"}, {"original": ".el-tree-node:focus>.el-tree-node__content", "type": "class", "names": ["el-tree-node", "el-tree-node__content"], "fullRule": "\r\n\r\n.el-tree-node:focus>.el-tree-node__content {\r\n  background: none;\r\n}"}, {"original": "height: 50px;\r\n\r\n  &:hover", "type": "tag", "names": ["height"], "fullRule": "\r\n  height: 50px;\r\n\r\n  &:hover {\r\n    background: var(--gray-200);\r\n  }"}, {"original": ".el-tree__drop-indicator", "type": "class", "names": ["el-tree__drop-indicator"], "fullRule": "\r\n\r\n.el-tree__drop-indicator {\r\n  height: 3px;\r\n}"}, {"original": "// 禁用动画提高性能\r\n.el-collapse-transition-enter-active", "type": "class", "names": ["el-collapse-transition-enter-active"], "fullRule": "\r\n\r\n// 禁用动画提高性能\r\n.el-collapse-transition-enter-active,\r\n.el-collapse-transition-leave-active {\r\n  transition: none !important;\r\n}"}, {"original": ".el-collapse-transition-leave-active", "type": "class", "names": ["el-collapse-transition-leave-active"], "fullRule": "\r\n\r\n// 禁用动画提高性能\r\n.el-collapse-transition-enter-active,\r\n.el-collapse-transition-leave-active {\r\n  transition: none !important;\r\n}"}], "totalSelectors": 21}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\apidoc\\params-tree\\g-params-tree2.vue", "unusedSelectors": [{"original": "width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    .el-input-number .el-input__inner", "type": "class", "names": ["el-input-number", "el-input__inner"], "fullRule": "\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    .el-input-number .el-input__inner {\r\n        text-align: left;\r\n    }"}, {"original": "border: none;\r\n        border-radius: 0;\r\n        border-color: var(--gray-400);\r\n        border-bottom: 1px solid var(--gray-400);\r\n        font-size: 12px;\r\n        &:focus", "type": "tag", "names": ["border"], "fullRule": "\r\n        border: none;\r\n        border-radius: 0;\r\n        border-color: var(--gray-400);\r\n        border-bottom: 1px solid var(--gray-400);\r\n        font-size: 12px;\r\n        &:focus {\r\n            border-bottom: 2px solid var(--theme-color);\r\n            margin-bottom: -1px;\r\n        }"}, {"original": "cursor: pointer;\r\n        background: var(--gray-300);\r\n        height: 25px;\r\n        line-height: 25px;\r\n        text-indent: 1em;\r\n        width: 98%;\r\n        position: relative;\r\n        &.active", "type": "class", "names": ["active"], "fullRule": "\r\n        cursor: pointer;\r\n        background: var(--gray-300);\r\n        height: 25px;\r\n        line-height: 25px;\r\n        text-indent: 1em;\r\n        width: 98%;\r\n        position: relative;\r\n        &.active {\r\n            background: none;\r\n            border: 1px solid var(--gray-300);\r\n            cursor: auto;\r\n        }"}, {"original": "position: absolute;\r\n            right: 3px;\r\n            top: 4px;\r\n            font-size: 16px;\r\n            cursor: pointer;\r\n            &:hover", "type": "tag", "names": ["position"], "fullRule": "\r\n            position: absolute;\r\n            right: 3px;\r\n            top: 4px;\r\n            font-size: 16px;\r\n            cursor: pointer;\r\n            &:hover {\r\n                color: var(--red);\r\n            }"}], "totalSelectors": 6}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\apidoc\\params-view\\g-params-view.vue", "unusedSelectors": [{"original": "display: inline-flex;\r\n    width: 100%;\r\n    overflow-y: auto;\r\n    max-height: 400px;\r\n    padding-bottom: 15px;\r\n\r\n    &::-webkit-scrollbar", "type": "tag", "names": ["display"], "fullRule": "\r\n    display: inline-flex;\r\n    width: 100%;\r\n    overflow-y: auto;\r\n    max-height: 400px;\r\n    padding-bottom: 15px;\r\n\r\n    &::-webkit-scrollbar {\r\n      background: #797e9d;\r\n    }"}, {"original": "min-height: 20px;\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n\r\n        &:hover", "type": "tag", "names": ["min"], "fullRule": "\r\n        min-height: 20px;\r\n        display: flex;\r\n        align-items: center;\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n\r\n        &:hover {\r\n          background: #434857;\r\n        }"}, {"original": "&.active", "type": "class", "names": ["active"], "fullRule": "\r\n\r\n        &.active {\r\n          background: var(--gray-700);\r\n        }"}, {"original": "&.error", "type": "class", "names": ["error"], "fullRule": "\r\n\r\n        &.error {\r\n          animation: blink 4s infinite alternate;\r\n        }"}, {"original": "display: inline-flex;\r\n        width: 12px;\r\n        height: 12px;\r\n        margin-right: 5px;\r\n        background: var(--gray-800);\r\n        border: 1px solid var(--gray-500);\r\n        cursor: pointer;\r\n\r\n        &:hover", "type": "tag", "names": ["display"], "fullRule": "\r\n        display: inline-flex;\r\n        width: 12px;\r\n        height: 12px;\r\n        margin-right: 5px;\r\n        background: var(--gray-800);\r\n        border: 1px solid var(--gray-500);\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          border: 1px solid var(--gray-300);\r\n        }"}, {"original": ".indent", "type": "class", "names": ["indent"], "fullRule": "\r\n\r\n      .indent {\r\n        user-select: text;\r\n        height: 20px;\r\n        flex: 0 0 8px;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }"}, {"original": ".path", "type": "class", "names": ["path"], "fullRule": "\r\n\r\n      .path {\r\n        color: #f8c555,\r\n      }"}, {"original": ".description", "type": "class", "names": ["description"], "fullRule": "\r\n\r\n      .description {\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        color: #6A9955;\r\n        cursor: text;\r\n      }"}, {"original": ".colon", "type": "class", "names": ["colon"], "fullRule": "\r\n\r\n      .colon {\r\n        margin-right: 5px;\r\n      }"}, {"original": ".colon", "type": "class", "names": ["colon"], "fullRule": "\r\n\r\n      .colon,\r\n      .bracket,\r\n      .comma,\r\n      .curly-brace {\r\n        color: #ccc;\r\n        font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;\r\n      }"}, {"original": ".bracket", "type": "class", "names": ["bracket"], "fullRule": "\r\n\r\n      .colon,\r\n      .bracket,\r\n      .comma,\r\n      .curly-brace {\r\n        color: #ccc;\r\n        font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;\r\n      }"}, {"original": ".comma", "type": "class", "names": ["comma"], "fullRule": "\r\n\r\n      .colon,\r\n      .bracket,\r\n      .comma,\r\n      .curly-brace {\r\n        color: #ccc;\r\n        font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;\r\n      }"}, {"original": ".curly-brace", "type": "class", "names": ["curly-brace"], "fullRule": "\r\n\r\n      .colon,\r\n      .bracket,\r\n      .comma,\r\n      .curly-brace {\r\n        color: #ccc;\r\n        font-family: source-code-pro, Menlo, Monaco, Consolas, Courier New, monospace;\r\n      }"}, {"original": "border: 1px solid transparent;\r\n\r\n        &.active", "type": "class", "names": ["active"], "fullRule": "\r\n        border: 1px solid transparent;\r\n\r\n        &.active {\r\n          color: var(--red);\r\n          border: 1px solid var(--gray-400);\r\n        }"}, {"original": "border: 1px solid transparent;\r\n\r\n        &.active", "type": "class", "names": ["active"], "fullRule": "\r\n        border: 1px solid transparent;\r\n\r\n        &.active {\r\n          color: var(--orange);\r\n          border: 1px solid var(--gray-400);\r\n        }"}, {"original": ".string-value", "type": "class", "names": ["string-value"], "fullRule": "\r\n\r\n      .string-value {\r\n        color: #7ec699;\r\n        font-size: .9em;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n        max-width: 50%;\r\n        flex: 0 0 auto;\r\n      }"}, {"original": ".boolean-value", "type": "class", "names": ["boolean-value"], "fullRule": "\r\n\r\n      .boolean-value {\r\n        color: #cc99cd;\r\n        font-size: .9em;\r\n      }"}, {"original": ".number-value", "type": "class", "names": ["number-value"], "fullRule": "\r\n\r\n      .number-value {\r\n        color: #ccc;\r\n        font-size: .9em;\r\n      }"}, {"original": ".null-value", "type": "class", "names": ["null-value"], "fullRule": "\r\n\r\n      .null-value {\r\n        color: #f60;\r\n        font-size: .9em;\r\n      }"}, {"original": ".file-value", "type": "class", "names": ["file-value"], "fullRule": "\r\n\r\n      .file-value {\r\n        color: #7ec699;\r\n        font-size: .9em;\r\n      }"}], "totalSelectors": 24}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\apidoc\\virtual-scroll\\g-virtual-scroll.vue", "unusedSelectors": [{"original": "background: var(--border-color", "type": "tag", "names": ["background"], "fullRule": "\r\n    background: var(--border-color, #dcdfe6);\r\n    border-radius: 4px;\r\n    \r\n    &:hover {\r\n      background: var(--border-color-darker, #c4c6cf);\r\n    }"}, {"original": "#dcdfe6);\r\n    border-radius: 4px;\r\n    \r\n    &:hover", "type": "id", "names": ["dcdfe6"], "fullRule": "\r\n    background: var(--border-color, #dcdfe6);\r\n    border-radius: 4px;\r\n    \r\n    &:hover {\r\n      background: var(--border-color-darker, #c4c6cf);\r\n    }"}], "totalSelectors": 7}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\card\\g-card.vue", "unusedSelectors": [{"original": "position: relative;\r\n    overflow-y: auto;\r\n\r\n    &.active", "type": "class", "names": ["active"], "fullRule": "\r\n    position: relative;\r\n    overflow-y: auto;\r\n\r\n    &.active {\r\n      padding: 0rem !important;\r\n      height: 0px !important;\r\n    }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\collapse-card\\g-collapse-card.vue", "unusedSelectors": [{"original": "width: 40px;\r\n        height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n\r\n        &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n        width: 40px;\r\n        height: 100%;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          background: var(--gray-300);\r\n        }"}, {"original": ".tail", "type": "class", "names": ["tail"], "fullRule": "\r\n\r\n    .tail {\r\n      padding-right: 20px;\r\n      margin-left: auto;\r\n    }"}, {"original": "cursor: not-allowed;\r\n\r\n        &:hover", "type": "tag", "names": ["cursor"], "fullRule": "\r\n        cursor: not-allowed;\r\n\r\n        &:hover {\r\n          background: none;\r\n        }"}, {"original": ".disabled-icon", "type": "class", "names": ["disabled-icon"], "fullRule": "\r\n\r\n  .disabled-icon {\r\n    width: 15px;\r\n    height: 15px;\r\n  }"}, {"original": "// 内容区域\r\n  .content", "type": "class", "names": ["content"], "fullRule": "\r\n\r\n  // 内容区域\r\n  .content {\r\n    flex: 1;\r\n    overflow: hidden;\r\n  }"}], "totalSelectors": 8}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\config\\g-config.vue", "unusedSelectors": [{"original": "padding: 10px 20px;\r\n\r\n  &:hover", "type": "tag", "names": ["padding"], "fullRule": "\r\n  padding: 10px 20px;\r\n\r\n  &:hover {\r\n    background: var(--gray-200);\r\n  }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\contextmenu\\g-contextmenu-item.vue", "unusedSelectors": [{"original": "color: var(--gray-400);\r\n    cursor: default;\r\n\r\n    &:hover", "type": "tag", "names": ["color"], "fullRule": "\r\n    color: var(--gray-400);\r\n    cursor: default;\r\n\r\n    &:hover {\r\n      background: inherit;\r\n      color: var(--gray-400);\r\n    }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\contextmenu\\g-contextmenu.vue", "unusedSelectors": [{"original": "from", "type": "tag", "names": ["from"], "fullRule": "\r\n    from {\r\n      // transform: scale(0.8);\r\n      opacity: 0;\r\n    }"}, {"original": "to", "type": "tag", "names": ["to"], "fullRule": "\r\n\r\n    to {\r\n      // transform: scale(1);\r\n      opacity: 1;\r\n    }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\ellipsis-content\\g-ellipsis-content.vue", "unusedSelectors": [{"original": ".copy", "type": "class", "names": ["copy"], "fullRule": "\r\n\r\n  .copy {\r\n    margin-right: 10px;\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\forms\\search\\g-search.vue", "unusedSelectors": [{"original": ".el-form-item", "type": "class", "names": ["el-form-item"], "fullRule": "\r\n  .el-form-item {\r\n    margin-bottom: 10px;\r\n  }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\remote-select\\g-remote-select-item.vue", "unusedSelectors": [{"original": "height: 38px;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  cursor: pointer;\r\n\r\n  &:hover", "type": "tag", "names": ["height"], "fullRule": "\r\n  height: 38px;\r\n  display: flex;\r\n  align-items: center;\r\n  padding: 0 20px;\r\n  cursor: pointer;\r\n\r\n  &:hover {\r\n    background: #eaf3fe;\r\n  }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\remote-select\\g-remote-select.vue", "unusedSelectors": [{"original": "width: 100%;\r\n    outline: 0;\r\n    padding: 0 15px;\r\n    height: 28px;\r\n    border: 1px solid var(--gray-300);\r\n    border-radius: var(--border-radius-sm);\r\n    color: var(--gray-700);\r\n    &::-webkit-input-placeholder", "type": "tag", "names": ["width"], "fullRule": "\r\n    width: 100%;\r\n    outline: 0;\r\n    padding: 0 15px;\r\n    height: 28px;\r\n    border: 1px solid var(--gray-300);\r\n    border-radius: var(--border-radius-sm);\r\n    color: var(--gray-700);\r\n    &::-webkit-input-placeholder {\r\n      color: var(--gray-500);\r\n    }"}], "totalSelectors": 4}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\sse-view\\components\\popover\\sse-popover.vue", "unusedSelectors": [{"original": "display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 5px 16px;\r\n    border-bottom: 1px solid var(--border-color-lighter", "type": "tag", "names": ["display"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 5px 16px;\r\n    border-bottom: 1px solid var(--border-color-lighter, #ebeef5);\r\n    background: linear-gradient(to right, #2c3e50, #3a4a5f);\r\n    color: var(--white, #ffffff);\r\n    border-top-left-radius: 5px;\r\n    border-top-right-radius: 5px;\r\n\r\n    .header {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      color: var(--white, #ffffff);\r\n    }"}, {"original": "#ebeef5);\r\n    background: linear-gradient(to right", "type": "id", "names": ["ebeef5"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 5px 16px;\r\n    border-bottom: 1px solid var(--border-color-lighter, #ebeef5);\r\n    background: linear-gradient(to right, #2c3e50, #3a4a5f);\r\n    color: var(--white, #ffffff);\r\n    border-top-left-radius: 5px;\r\n    border-top-right-radius: 5px;\r\n\r\n    .header {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      color: var(--white, #ffffff);\r\n    }"}, {"original": "width: 20px;\r\n      height: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 5px;\r\n      cursor: pointer;\r\n      color: var(--white", "type": "tag", "names": ["width"], "fullRule": "\r\n      width: 20px;\r\n      height: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 5px;\r\n      cursor: pointer;\r\n      color: var(--white, #ffffff);\r\n      transition: background-color 0.2s;\r\n\r\n      .iconfont {\r\n        font-size: 12px;\r\n      }"}, {"original": "padding: 8px 16px;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            color: var(--text-color-regular", "type": "tag", "names": ["padding"], "fullRule": "\r\n            padding: 8px 16px;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            color: var(--text-color-regular, #606266);\r\n            border-bottom: 2px solid transparent;\r\n            transition: all 0.2s;\r\n\r\n            &:hover {\r\n              color: var(--color-primary, #409eff);\r\n            }"}, {"original": ".raw-block", "type": "class", "names": ["raw-block"], "fullRule": "\r\n\r\n          .full-content,\r\n          .raw-block {\r\n            background-color: var(--fill-color-extra-light, #fafcff);\r\n            border: 1px solid var(--border-color-lighter, #ebeef5);\r\n            border-radius: 4px;\r\n            padding: 12px;\r\n            margin: 0;\r\n            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;\r\n            font-size: 13px;\r\n            color: var(--text-color-primary, #303133);\r\n            white-space: pre-wrap;\r\n            word-break: break-all;\r\n            max-height: 350px;\r\n            overflow-y: auto;\r\n            line-height: 1.4;\r\n          }"}, {"original": ".raw-block", "type": "class", "names": ["raw-block"], "fullRule": "\r\n\r\n          .raw-block {\r\n            background-color: var(--color-info-light-9, #f4f4f5);\r\n            color: var(--text-color-secondary, #909399);\r\n          }"}], "totalSelectors": 16}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\sse-view\\g-sse-view.vue", "unusedSelectors": [{"original": "flex: 1;\r\n          transition: all 0.3s ease;\r\n\r\n          :deep(.el-input__suffix)", "type": "class", "names": ["el-input__suffix"], "fullRule": "\r\n          flex: 1;\r\n          transition: all 0.3s ease;\r\n\r\n          :deep(.el-input__suffix) {\r\n            display: flex;\r\n            align-items: center;\r\n          }"}, {"original": "height: 100%;\r\n            width: 25px;\r\n            font-family: '<PERSON><PERSON><PERSON>'", "type": "tag", "names": ["height"], "fullRule": "\r\n            height: 100%;\r\n            width: 25px;\r\n            font-family: '<PERSON><PERSON><PERSON>', 'Monaco', 'Courier New', monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular, #606266);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.2s;\r\n            user-select: none;\r\n            flex-shrink: 0;\r\n            margin-left: 4px;\r\n\r\n            &:hover {\r\n              background-color: var(--fill-color, #f0f2f5);\r\n              border-color: var(--border-color, #dcdfe6);\r\n            }"}, {"original": "monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular", "type": "tag", "names": ["monospace"], "fullRule": "\r\n            height: 100%;\r\n            width: 25px;\r\n            font-family: '<PERSON><PERSON><PERSON>', 'Monaco', 'Courier New', monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular, #606266);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.2s;\r\n            user-select: none;\r\n            flex-shrink: 0;\r\n            margin-left: 4px;\r\n\r\n            &:hover {\r\n              background-color: var(--fill-color, #f0f2f5);\r\n              border-color: var(--border-color, #dcdfe6);\r\n            }"}, {"original": "background-color: var(--color-primary", "type": "tag", "names": ["background"], "fullRule": "\r\n              background-color: var(--color-primary, #409eff);\r\n              border-color: var(--color-primary, #409eff);\r\n              color: #ffffff;\r\n\r\n              &:hover {\r\n                background-color: var(--color-primary-light-3, #79bbff);\r\n                border-color: var(--color-primary-light-3, #79bbff);\r\n              }"}, {"original": "#409eff);\r\n              color: #ffffff;\r\n\r\n              &:hover", "type": "id", "names": ["ffffff"], "fullRule": "\r\n              background-color: var(--color-primary, #409eff);\r\n              border-color: var(--color-primary, #409eff);\r\n              color: #ffffff;\r\n\r\n              &:hover {\r\n                background-color: var(--color-primary-light-3, #79bbff);\r\n                border-color: var(--color-primary-light-3, #79bbff);\r\n              }"}, {"original": ".action-icons", "type": "class", "names": ["action-icons"], "fullRule": "\r\n\r\n      .action-icons {\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n        height: 28px;\r\n        margin-left: auto;\r\n      }"}, {"original": ".icon", "type": "class", "names": ["icon"], "fullRule": "\r\n      .icon {\r\n        margin: 0 1px;\r\n      }"}, {"original": "width: 28px;\r\n        height: 28px;\r\n        color: var(--text-color-regular", "type": "tag", "names": ["width"], "fullRule": "\r\n        width: 28px;\r\n        height: 28px;\r\n        color: var(--text-color-regular, #606266);\r\n        cursor: pointer;\r\n        transition: all 0.2s;\r\n\r\n        &:hover {\r\n          color: var(--color-primary, #409eff);\r\n          background-color: #efefef;\r\n        }"}, {"original": "width: 28px;\r\n        height: 28px;\r\n        color: var(--text-color-regular", "type": "tag", "names": ["width"], "fullRule": "\r\n        width: 28px;\r\n        height: 28px;\r\n        color: var(--text-color-regular, #606266);\r\n        cursor: pointer;\r\n        transition: color 0.2s;\r\n\r\n        &:hover {\r\n          color: var(--color-success, #67c23a);\r\n          background-color: #efefef;\r\n        }"}, {"original": "width: 28px;\r\n        height: 28px;\r\n        color: var(--text-color-regular", "type": "tag", "names": ["width"], "fullRule": "\r\n        width: 28px;\r\n        height: 28px;\r\n        color: var(--text-color-regular, #606266);\r\n        cursor: pointer;\r\n        transition: all 0.2s;\r\n\r\n        &:hover {\r\n          color: var(--color-primary, #409eff);\r\n          background-color: #efefef;\r\n        }"}, {"original": "font-size: 12px;\r\n          padding: 8px 12px;\r\n          margin: 0;\r\n\r\n          &:not(.error):not(.no-result)", "type": "class", "names": ["error", "no-result"], "fullRule": "\r\n          font-size: 12px;\r\n          padding: 8px 12px;\r\n          margin: 0;\r\n\r\n          &:not(.error):not(.no-result) {\r\n            color: var(--color-success, #67c23a);\r\n          }"}, {"original": "&.no-result", "type": "class", "names": ["no-result"], "fullRule": "\r\n\r\n          &.no-result {\r\n            color: var(--color-warning, #e6a23c);\r\n          }"}, {"original": "&.error", "type": "class", "names": ["error"], "fullRule": "\r\n\r\n          &.error {\r\n            color: var(--color-danger, #f56c6c);\r\n          }"}, {"original": "flex: 1;\r\n          max-width: none;\r\n\r\n          :deep(.el-input__suffix)", "type": "class", "names": ["el-input__suffix"], "fullRule": "\r\n          flex: 1;\r\n          max-width: none;\r\n\r\n          :deep(.el-input__suffix) {\r\n            display: flex;\r\n            align-items: center;\r\n          }"}, {"original": "height: 25px;\r\n            width: 25px;\r\n            font-family: '<PERSON><PERSON><PERSON>'", "type": "tag", "names": ["height"], "fullRule": "\r\n            height: 25px;\r\n            width: 25px;\r\n            font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            background-color: var(--fill-color-light, #f5f7fa);\r\n            border: 1px solid var(--border-color-light, #e4e7ed);\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular, #606266);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.2s;\r\n            user-select: none;\r\n            flex-shrink: 0;\r\n            margin-left: 4px;\r\n\r\n            &:hover {\r\n              background-color: var(--fill-color, #f0f2f5);\r\n              border-color: var(--border-color, #dcdfe6);\r\n            }"}, {"original": "monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            background-color: var(--fill-color-light", "type": "tag", "names": ["monospace"], "fullRule": "\r\n            height: 25px;\r\n            width: 25px;\r\n            font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            background-color: var(--fill-color-light, #f5f7fa);\r\n            border: 1px solid var(--border-color-light, #e4e7ed);\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular, #606266);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.2s;\r\n            user-select: none;\r\n            flex-shrink: 0;\r\n            margin-left: 4px;\r\n\r\n            &:hover {\r\n              background-color: var(--fill-color, #f0f2f5);\r\n              border-color: var(--border-color, #dcdfe6);\r\n            }"}, {"original": "#f5f7fa);\r\n            border: 1px solid var(--border-color-light", "type": "id", "names": ["f5f7fa"], "fullRule": "\r\n            height: 25px;\r\n            width: 25px;\r\n            font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            background-color: var(--fill-color-light, #f5f7fa);\r\n            border: 1px solid var(--border-color-light, #e4e7ed);\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular, #606266);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.2s;\r\n            user-select: none;\r\n            flex-shrink: 0;\r\n            margin-left: 4px;\r\n\r\n            &:hover {\r\n              background-color: var(--fill-color, #f0f2f5);\r\n              border-color: var(--border-color, #dcdfe6);\r\n            }"}, {"original": "#e4e7ed);\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular", "type": "id", "names": ["e4e7ed"], "fullRule": "\r\n            height: 25px;\r\n            width: 25px;\r\n            font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;\r\n            font-weight: bold;\r\n            font-size: 10px;\r\n            background-color: var(--fill-color-light, #f5f7fa);\r\n            border: 1px solid var(--border-color-light, #e4e7ed);\r\n            border-radius: 3px;\r\n            color: var(--text-color-regular, #606266);\r\n            cursor: pointer;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            transition: all 0.2s;\r\n            user-select: none;\r\n            flex-shrink: 0;\r\n            margin-left: 4px;\r\n\r\n            &:hover {\r\n              background-color: var(--fill-color, #f0f2f5);\r\n              border-color: var(--border-color, #dcdfe6);\r\n            }"}, {"original": "background-color: var(--color-primary", "type": "tag", "names": ["background"], "fullRule": "\r\n              background-color: var(--color-primary, #409eff);\r\n              border-color: var(--color-primary, #409eff);\r\n              color: #ffffff;\r\n\r\n              &:hover {\r\n                background-color: var(--color-primary-light-3, #79bbff);\r\n                border-color: var(--color-primary-light-3, #79bbff);\r\n              }"}, {"original": "#409eff);\r\n              color: #ffffff;\r\n\r\n              &:hover", "type": "id", "names": ["ffffff"], "fullRule": "\r\n              background-color: var(--color-primary, #409eff);\r\n              border-color: var(--color-primary, #409eff);\r\n              color: #ffffff;\r\n\r\n              &:hover {\r\n                background-color: var(--color-primary-light-3, #79bbff);\r\n                border-color: var(--color-primary-light-3, #79bbff);\r\n              }"}, {"original": "width: 24px;\r\n          height: 24px;\r\n          color: var(--text-color-regular", "type": "tag", "names": ["width"], "fullRule": "\r\n          width: 24px;\r\n          height: 24px;\r\n          color: var(--text-color-regular, #606266);\r\n          cursor: pointer;\r\n          transition: color 0.2s;\r\n          flex-shrink: 0;\r\n\r\n          &:hover {\r\n            color: var(--color-danger, #f56c6c);\r\n          }"}, {"original": "font-size: 12px;\r\n\r\n          &:not(.error):not(.no-result)", "type": "class", "names": ["error", "no-result"], "fullRule": "\r\n          font-size: 12px;\r\n\r\n          &:not(.error):not(.no-result) {\r\n            color: var(--color-success, #67c23a);\r\n          }"}, {"original": "&.no-result", "type": "class", "names": ["no-result"], "fullRule": "\r\n\r\n          &.no-result {\r\n            color: var(--color-warning, #e6a23c);\r\n          }"}, {"original": "&.error", "type": "class", "names": ["error"], "fullRule": "\r\n\r\n          &.error {\r\n            color: var(--color-danger, #f56c6c);\r\n          }"}, {"original": "display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex-direction: column;\r\n    height: 200px;\r\n    color: var(--text-color-secondary", "type": "tag", "names": ["display"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex-direction: column;\r\n    height: 200px;\r\n    color: var(--text-color-secondary, #909399);\r\n    font-size: 14px;\r\n    gap: 12px;\r\n\r\n    .loading-icon {\r\n      font-size: 24px;\r\n      animation: loading-rotate 2s linear infinite;\r\n    }"}, {"original": "#909399);\r\n    font-size: 14px;\r\n    gap: 12px;\r\n\r\n    .loading-icon", "type": "class", "names": ["loading-icon"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex-direction: column;\r\n    height: 200px;\r\n    color: var(--text-color-secondary, #909399);\r\n    font-size: 14px;\r\n    gap: 12px;\r\n\r\n    .loading-icon {\r\n      font-size: 24px;\r\n      animation: loading-rotate 2s linear infinite;\r\n    }"}, {"original": "span", "type": "tag", "names": ["span"], "fullRule": "\r\n\r\n    span {\r\n      font-size: 14px;\r\n    }"}, {"original": "from", "type": "tag", "names": ["from"], "fullRule": "\r\n    from {\r\n      transform: rotate(0deg);\r\n    }"}, {"original": "to", "type": "tag", "names": ["to"], "fullRule": "\r\n\r\n    to {\r\n      transform: rotate(360deg);\r\n    }"}, {"original": "flex: 1;\r\n    overflow: auto;\r\n    padding: 12px;\r\n    background-color: var(--fill-color-extra-light", "type": "tag", "names": ["flex"], "fullRule": "\r\n    flex: 1;\r\n    overflow: auto;\r\n    padding: 12px;\r\n    background-color: var(--fill-color-extra-light, #fafcff);\r\n    border: 1px solid var(--border-color-lighter, #ebeef5);\r\n    border-radius: 4px;\r\n    margin: 0 12px 12px 12px;\r\n\r\n    .raw-data {\r\n      margin: 0;\r\n      padding: 0;\r\n      font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;\r\n      font-size: 13px;\r\n      color: var(--text-color-primary, #303133);\r\n      white-space: pre-wrap;\r\n      word-break: break-all;\r\n      line-height: 1.4;\r\n      background: none;\r\n      border: none;\r\n    }"}, {"original": "#fafcff);\r\n    border: 1px solid var(--border-color-lighter", "type": "id", "names": ["fafcff"], "fullRule": "\r\n    flex: 1;\r\n    overflow: auto;\r\n    padding: 12px;\r\n    background-color: var(--fill-color-extra-light, #fafcff);\r\n    border: 1px solid var(--border-color-lighter, #ebeef5);\r\n    border-radius: 4px;\r\n    margin: 0 12px 12px 12px;\r\n\r\n    .raw-data {\r\n      margin: 0;\r\n      padding: 0;\r\n      font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;\r\n      font-size: 13px;\r\n      color: var(--text-color-primary, #303133);\r\n      white-space: pre-wrap;\r\n      word-break: break-all;\r\n      line-height: 1.4;\r\n      background: none;\r\n      border: none;\r\n    }"}, {"original": "#ebeef5);\r\n    border-radius: 4px;\r\n    margin: 0 12px 12px 12px;\r\n\r\n    .raw-data", "type": "class", "names": ["raw-data"], "fullRule": "\r\n    flex: 1;\r\n    overflow: auto;\r\n    padding: 12px;\r\n    background-color: var(--fill-color-extra-light, #fafcff);\r\n    border: 1px solid var(--border-color-lighter, #ebeef5);\r\n    border-radius: 4px;\r\n    margin: 0 12px 12px 12px;\r\n\r\n    .raw-data {\r\n      margin: 0;\r\n      padding: 0;\r\n      font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace;\r\n      font-size: 13px;\r\n      color: var(--text-color-primary, #303133);\r\n      white-space: pre-wrap;\r\n      word-break: break-all;\r\n      line-height: 1.4;\r\n      background: none;\r\n      border: none;\r\n    }"}, {"original": "display: flex;\r\n      align-items: center;\r\n      padding: 4px 12px 4px 0;\r\n      height: 100%;\r\n      border-radius: 4px;\r\n      background-color: var(--bg-color", "type": "tag", "names": ["display"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 4px 12px 4px 0;\r\n      height: 100%;\r\n      border-radius: 4px;\r\n      background-color: var(--bg-color, #ffffff);\r\n\r\n      &.sse-message-hex {\r\n        border-left: 3px solid var(--color-warning, #e6a23c);\r\n        background-color: var(--color-warning-light-9, #fdf6ec);\r\n      }"}, {"original": "#ffffff);\r\n\r\n      &.sse-message-hex", "type": "class", "names": ["sse-message-hex"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 4px 12px 4px 0;\r\n      height: 100%;\r\n      border-radius: 4px;\r\n      background-color: var(--bg-color, #ffffff);\r\n\r\n      &.sse-message-hex {\r\n        border-left: 3px solid var(--color-warning, #e6a23c);\r\n        background-color: var(--color-warning-light-9, #fdf6ec);\r\n      }"}, {"original": ".message-index", "type": "class", "names": ["message-index"], "fullRule": "\r\n\r\n      .message-index {\r\n        font-size: 12px;\r\n        color: var(--color-primary, #409eff);\r\n        font-weight: bold;\r\n        min-width: 30px;\r\n        text-align: right;\r\n        margin-right: 12px;\r\n        flex-shrink: 0;\r\n      }"}, {"original": ".message-content", "type": "class", "names": ["message-content"], "fullRule": "\r\n\r\n      .message-content {\r\n        flex: 1;\r\n        min-width: 0;\r\n        font-family: '<PERSON><PERSON><PERSON>', 'Monaco', 'Courier New', monospace;\r\n        font-size: 13px;\r\n        color: var(--text-color-primary, #303133);\r\n        white-space: nowrap;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        margin: 0 12px 0 0;\r\n      }"}, {"original": ".message-timestamp", "type": "class", "names": ["message-timestamp"], "fullRule": "\r\n\r\n      .message-timestamp {\r\n        font-size: 11px;\r\n        color: var(--text-color-secondary, #909399);\r\n        font-family: '<PERSON>solas', 'Monaco', 'Courier New', monospace;\r\n        min-width: 80px;\r\n        text-align: right;\r\n        flex-shrink: 0;\r\n      }"}, {"original": "background-color: var(--fill-color-lighter", "type": "tag", "names": ["background"], "fullRule": "\r\n        background-color: var(--fill-color-lighter, #f2f6fc);\r\n        cursor: pointer;\r\n\r\n        &.sse-message-hex {\r\n          background-color: var(--color-warning-light-8, #faecd8);\r\n        }"}, {"original": "#f2f6fc);\r\n        cursor: pointer;\r\n\r\n        &.sse-message-hex", "type": "class", "names": ["sse-message-hex"], "fullRule": "\r\n        background-color: var(--fill-color-lighter, #f2f6fc);\r\n        cursor: pointer;\r\n\r\n        &.sse-message-hex {\r\n          background-color: var(--color-warning-light-8, #faecd8);\r\n        }"}, {"original": "// 高亮样式\r\n:deep(.highlight)", "type": "class", "names": ["highlight"], "fullRule": "\r\n\r\n// 高亮样式\r\n:deep(.highlight) {\r\n  background-color: var(--color-warning-light-7, #fdf2d5);\r\n  color: var(--color-warning-dark-2, #b17a1a);\r\n  font-weight: 600;\r\n  padding: 1px 2px;\r\n  border-radius: 2px;\r\n}"}], "totalSelectors": 42}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\valid-input\\g-valid-input.vue", "unusedSelectors": [{"original": "width: 100%;\r\n    height: 29px;\r\n    .el-textarea__inner", "type": "class", "names": ["el-textarea__inner"], "fullRule": "\r\n    width: 100%;\r\n    height: 29px;\r\n    .el-textarea__inner {\r\n      font-size: 12px;\r\n    }"}, {"original": "width: 100%;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      border: 1px solid var(--gray-400);\r\n      border-radius: 4px;\r\n      padding: 0 10px;\r\n      font-size: 12px;\r\n      color: var(--el-input-text-color", "type": "tag", "names": ["width"], "fullRule": "\r\n      width: 100%;\r\n      height: 100%;\r\n      display: flex;\r\n      align-items: center;\r\n      border: 1px solid var(--gray-400);\r\n      border-radius: 4px;\r\n      padding: 0 10px;\r\n      font-size: 12px;\r\n      color: var(--el-input-text-color, var(--el-text-color-regular));\r\n\r\n      &.disabled {\r\n        cursor: not-allowed;\r\n        //保持与elementui样式统一\r\n        background-color: var(--el-disabled-bg-color);\r\n        border-color: var(--el-disabled-border-color);\r\n        color: var(--el-disabled-text-color);\r\n      }"}, {"original": "text-indent: -1px;\r\n        width: 100%;\r\n        font-size: 12px;\r\n        color: var(--el-input-text-color", "type": "tag", "names": ["text"], "fullRule": "\r\n        text-indent: -1px;\r\n        width: 100%;\r\n        font-size: 12px;\r\n        color: var(--el-input-text-color, var(--el-text-color-regular));\r\n        border-radius: 0;\r\n\r\n        &::-webkit-scrollbar {\r\n          width: 3px;\r\n          height: 3px;\r\n        }"}, {"original": "var(--el-text-color-regular));\r\n        border-radius: 0;\r\n\r\n        &::-webkit-scrollbar", "type": "tag", "names": ["var"], "fullRule": "\r\n        text-indent: -1px;\r\n        width: 100%;\r\n        font-size: 12px;\r\n        color: var(--el-input-text-color, var(--el-text-color-regular));\r\n        border-radius: 0;\r\n\r\n        &::-webkit-scrollbar {\r\n          width: 3px;\r\n          height: 3px;\r\n        }"}, {"original": "top: 42px;\r\n    left: 0;\r\n    background: var(--white);\r\n    z-index: var(--zIndex-contextmenu);\r\n    position: fixed;\r\n    min-width: 200px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: rgb(0 0 0 / 10%) 0px 2px 8px 0px; //墨刀弹窗样式\r\n    max-height: 220px;\r\n    overflow-y: auto;\r\n\r\n    &::-webkit-scrollbar", "type": "tag", "names": ["top"], "fullRule": "\r\n    top: 42px;\r\n    left: 0;\r\n    background: var(--white);\r\n    z-index: var(--zIndex-contextmenu);\r\n    position: fixed;\r\n    min-width: 200px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: rgb(0 0 0 / 10%) 0px 2px 8px 0px; //墨刀弹窗样式\r\n    max-height: 220px;\r\n    overflow-y: auto;\r\n\r\n    &::-webkit-scrollbar {\r\n      width: 5px;\r\n    }"}], "totalSelectors": 11}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\websocket-view\\components\\filter\\websocket-filter.vue", "unusedSelectors": [{"original": "height: 100%;\r\n          width: 25px;\r\n          font-family: '<PERSON><PERSON><PERSON>'", "type": "tag", "names": ["height"], "fullRule": "\r\n          height: 100%;\r\n          width: 25px;\r\n          font-family: '<PERSON><PERSON><PERSON>', 'Monaco', 'Courier New', monospace;\r\n          font-weight: bold;\r\n          font-size: 10px;\r\n          border-radius: 3px;\r\n          color: var(--text-color-regular, #606266);\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          transition: all 0.2s;\r\n          user-select: none;\r\n          flex-shrink: 0;\r\n          margin-left: 4px;\r\n\r\n          &:hover {\r\n            background-color: var(--fill-color, #f0f2f5);\r\n            border-color: var(--border-color, #dcdfe6);\r\n          }"}, {"original": "monospace;\r\n          font-weight: bold;\r\n          font-size: 10px;\r\n          border-radius: 3px;\r\n          color: var(--text-color-regular", "type": "tag", "names": ["monospace"], "fullRule": "\r\n          height: 100%;\r\n          width: 25px;\r\n          font-family: '<PERSON><PERSON><PERSON>', 'Monaco', 'Courier New', monospace;\r\n          font-weight: bold;\r\n          font-size: 10px;\r\n          border-radius: 3px;\r\n          color: var(--text-color-regular, #606266);\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          transition: all 0.2s;\r\n          user-select: none;\r\n          flex-shrink: 0;\r\n          margin-left: 4px;\r\n\r\n          &:hover {\r\n            background-color: var(--fill-color, #f0f2f5);\r\n            border-color: var(--border-color, #dcdfe6);\r\n          }"}, {"original": "background-color: var(--color-primary", "type": "tag", "names": ["background"], "fullRule": "\r\n            background-color: var(--color-primary, #409eff);\r\n            border-color: var(--color-primary, #409eff);\r\n            color: #ffffff;\r\n\r\n            &:hover {\r\n              background-color: var(--color-primary-light-3, #79bbff);\r\n              border-color: var(--color-primary-light-3, #79bbff);\r\n            }"}, {"original": "#409eff);\r\n            color: #ffffff;\r\n\r\n            &:hover", "type": "id", "names": ["ffffff"], "fullRule": "\r\n            background-color: var(--color-primary, #409eff);\r\n            border-color: var(--color-primary, #409eff);\r\n            color: #ffffff;\r\n\r\n            &:hover {\r\n              background-color: var(--color-primary-light-3, #79bbff);\r\n              border-color: var(--color-primary-light-3, #79bbff);\r\n            }"}, {"original": "display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n      height: 35px;\r\n      flex: 0 0 40%;\r\n      margin-left: auto;\r\n      .message-type-filter", "type": "class", "names": ["message-type-filter"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: flex-end;\r\n      height: 35px;\r\n      flex: 0 0 40%;\r\n      margin-left: auto;\r\n      .message-type-filter {\r\n        min-width: 140px;\r\n        width: 65%;\r\n        margin-right: 8px;\r\n      }"}, {"original": "margin: 0 1px;\r\n      width: 28px;\r\n      height: 28px;\r\n      color: var(--text-color-regular", "type": "tag", "names": ["margin"], "fullRule": "\r\n      margin: 0 1px;\r\n      width: 28px;\r\n      height: 28px;\r\n      color: var(--text-color-regular, #606266);\r\n      cursor: pointer;\r\n      transition: all 0.2s;\r\n\r\n      &:hover {\r\n        color: var(--color-primary, #409eff);\r\n        background-color: #efefef;\r\n      }"}, {"original": ".download-icon:hover", "type": "class", "names": ["download-icon"], "fullRule": "\r\n\r\n    .download-icon:hover {\r\n      color: var(--color-success, #67c23a);\r\n    }"}, {"original": ".clear-icon:hover", "type": "class", "names": ["clear-icon"], "fullRule": "\r\n    .clear-icon:hover {\r\n      color: var(--color-danger, #f56c6c);\r\n    }"}, {"original": "font-size: 12px;\r\n        padding: 8px 12px;\r\n        margin: 0;\r\n\r\n        &:not(.error):not(.no-result)", "type": "class", "names": ["error", "no-result"], "fullRule": "\r\n        font-size: 12px;\r\n        padding: 8px 12px;\r\n        margin: 0;\r\n\r\n        &:not(.error):not(.no-result) {\r\n          color: var(--color-success, #67c23a);\r\n        }"}, {"original": "&.no-result", "type": "class", "names": ["no-result"], "fullRule": "\r\n\r\n        &.no-result {\r\n          color: var(--color-warning, #e6a23c);\r\n        }"}, {"original": "&.error", "type": "class", "names": ["error"], "fullRule": "\r\n\r\n        &.error {\r\n          color: var(--color-danger, #f56c6c);\r\n        }"}], "totalSelectors": 12}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\websocket-view\\components\\popover\\websocket-popover.vue", "unusedSelectors": [{"original": "display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 5px 16px;\r\n    border-bottom: 1px solid var(--border-color-lighter", "type": "tag", "names": ["display"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 5px 16px;\r\n    border-bottom: 1px solid var(--border-color-lighter, #ebeef5);\r\n    background: linear-gradient(to right, #2c3e50, #3a4a5f);\r\n    color: var(--white, #ffffff);\r\n    border-top-left-radius: 5px;\r\n    border-top-right-radius: 5px;\r\n\r\n    .header {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      color: var(--white, #ffffff);\r\n    }"}, {"original": "#ebeef5);\r\n    background: linear-gradient(to right", "type": "id", "names": ["ebeef5"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    padding: 5px 16px;\r\n    border-bottom: 1px solid var(--border-color-lighter, #ebeef5);\r\n    background: linear-gradient(to right, #2c3e50, #3a4a5f);\r\n    color: var(--white, #ffffff);\r\n    border-top-left-radius: 5px;\r\n    border-top-right-radius: 5px;\r\n\r\n    .header {\r\n      margin: 0;\r\n      font-size: 16px;\r\n      color: var(--white, #ffffff);\r\n    }"}, {"original": "width: 20px;\r\n      height: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 5px;\r\n      cursor: pointer;\r\n      color: var(--white", "type": "tag", "names": ["width"], "fullRule": "\r\n      width: 20px;\r\n      height: 20px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      padding: 5px;\r\n      cursor: pointer;\r\n      color: var(--white, #ffffff);\r\n      transition: background-color 0.2s;\r\n\r\n      .iconfont {\r\n        font-size: 12px;\r\n      }"}, {"original": "padding: 2px 8px;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-weight: 500;\r\n\r\n          &.type-send", "type": "class", "names": ["type-send"], "fullRule": "\r\n          padding: 2px 8px;\r\n          border-radius: 4px;\r\n          font-size: 12px;\r\n          font-weight: 500;\r\n\r\n          &.type-send {\r\n            background-color: var(--color-primary-light-9, #ecf5ff);\r\n            color: var(--color-primary, #409eff);\r\n          }"}, {"original": "&.type-receive", "type": "class", "names": ["type-receive"], "fullRule": "\r\n\r\n          &.type-receive {\r\n            background-color: var(--color-success-light-9, #f0f9ff);\r\n            color: var(--color-success, #67c23a);\r\n          }"}, {"original": "&.type-connected", "type": "class", "names": ["type-connected"], "fullRule": "\r\n\r\n          &.type-connected {\r\n            background-color: var(--color-success-light-9, #f0f9ff);\r\n            color: var(--color-success, #67c23a);\r\n          }"}, {"original": "&.type-disconnected", "type": "class", "names": ["type-disconnected"], "fullRule": "\r\n\r\n          &.type-disconnected {\r\n            background-color: var(--color-warning-light-9, #fdf6ec);\r\n            color: var(--color-warning, #e6a23c);\r\n          }"}, {"original": "&.type-error", "type": "class", "names": ["type-error"], "fullRule": "\r\n\r\n          &.type-error {\r\n            background-color: var(--color-danger-light-9, #fef0f0);\r\n            color: var(--color-danger, #f56c6c);\r\n          }"}, {"original": "&.type-heartbeat", "type": "class", "names": ["type-heartbeat"], "fullRule": "\r\n\r\n          &.type-heartbeat {\r\n            background-color: var(--color-info-light-9, #f4f4f5);\r\n            color: var(--color-info, #909399);\r\n          }"}, {"original": "&.type-startConnect", "type": "class", "names": ["type-startConnect"], "fullRule": "\r\n\r\n          &.type-startConnect {\r\n            background-color: var(--color-primary-light-9, #ecf5ff);\r\n            color: var(--color-primary, #409eff);\r\n          }"}, {"original": "&.type-reconnecting", "type": "class", "names": ["type-reconnecting"], "fullRule": "\r\n\r\n          &.type-reconnecting {\r\n            background-color: var(--color-warning-light-9, #fdf6ec);\r\n            color: var(--color-warning, #e6a23c);\r\n          }"}, {"original": "padding: 8px 16px;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            color: var(--text-color-regular", "type": "tag", "names": ["padding"], "fullRule": "\r\n            padding: 8px 16px;\r\n            cursor: pointer;\r\n            font-size: 14px;\r\n            color: var(--text-color-regular, #606266);\r\n            border-bottom: 2px solid transparent;\r\n            transition: all 0.2s;\r\n\r\n            &:hover {\r\n              color: var(--color-primary, #409eff);\r\n            }"}], "totalSelectors": 20}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\components\\common\\websocket-view\\g-websocket-view.vue", "unusedSelectors": [{"original": "display: flex;\r\n      align-items: center;\r\n      padding: 6px 12px 6px 0;\r\n      height: 100%;\r\n      border-radius: 4px;\r\n      background-color: var(--bg-color", "type": "tag", "names": ["display"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 6px 12px 6px 0;\r\n      height: 100%;\r\n      border-radius: 4px;\r\n      background-color: var(--bg-color, #ffffff);\r\n      cursor: pointer;\r\n\r\n      .message-index {\r\n        font-size: 12px;\r\n        color: var(--gray-600);\r\n        min-width: 30px;\r\n        text-align: right;\r\n        margin-right: 10px;\r\n      }"}, {"original": "border-radius: 3px;\r\n        font-size: 14px;\r\n        min-width: 20px;\r\n        text-align: center;\r\n        margin-right: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        &.type-send", "type": "class", "names": ["type-send"], "fullRule": "\r\n        border-radius: 3px;\r\n        font-size: 14px;\r\n        min-width: 20px;\r\n        text-align: center;\r\n        margin-right: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        &.type-send,\r\n        &.type-heartbeat {\r\n          color: var(--color-success, #67c23a);\r\n        }"}, {"original": "&.type-heartbeat", "type": "class", "names": ["type-heartbeat"], "fullRule": "\r\n        border-radius: 3px;\r\n        font-size: 14px;\r\n        min-width: 20px;\r\n        text-align: center;\r\n        margin-right: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n\r\n        &.type-send,\r\n        &.type-heartbeat {\r\n          color: var(--color-success, #67c23a);\r\n        }"}, {"original": "&.type-receive", "type": "class", "names": ["type-receive"], "fullRule": "\r\n\r\n        &.type-receive {\r\n          color: var(--color-danger, #f56c6c);\r\n        }"}, {"original": "border-left: 3px solid var(--color-danger", "type": "tag", "names": ["border"], "fullRule": "\r\n        border-left: 3px solid var(--color-danger, #f56c6c);\r\n        background-color: var(--color-danger-light-9, #fef0f0);\r\n\r\n        &:hover {\r\n          background-color: var(--color-danger-light-8, #fde2e2);\r\n        }"}, {"original": "#f56c6c);\r\n        background-color: var(--color-danger-light-9", "type": "id", "names": ["f56c6c"], "fullRule": "\r\n        border-left: 3px solid var(--color-danger, #f56c6c);\r\n        background-color: var(--color-danger-light-9, #fef0f0);\r\n\r\n        &:hover {\r\n          background-color: var(--color-danger-light-8, #fde2e2);\r\n        }"}, {"original": "#fef0f0);\r\n\r\n        &:hover", "type": "id", "names": ["fef0f0"], "fullRule": "\r\n        border-left: 3px solid var(--color-danger, #f56c6c);\r\n        background-color: var(--color-danger-light-9, #fef0f0);\r\n\r\n        &:hover {\r\n          background-color: var(--color-danger-light-8, #fde2e2);\r\n        }"}, {"original": "border-left: 3px solid var(--color-success", "type": "tag", "names": ["border"], "fullRule": "\r\n        border-left: 3px solid var(--color-success, #67c23a);\r\n        background-color: var(--color-success-light-9, #f0f9ff);\r\n\r\n        &:hover {\r\n          background-color: var(--color-success-light-8, #e1f3d8);\r\n        }"}, {"original": "#f0f9ff);\r\n\r\n        &:hover", "type": "id", "names": ["f0f9ff"], "fullRule": "\r\n        border-left: 3px solid var(--color-success, #67c23a);\r\n        background-color: var(--color-success-light-9, #f0f9ff);\r\n\r\n        &:hover {\r\n          background-color: var(--color-success-light-8, #e1f3d8);\r\n        }"}, {"original": "border-left: 3px solid var(--color-warning", "type": "tag", "names": ["border"], "fullRule": "\r\n        border-left: 3px solid var(--color-warning, #e6a23c);\r\n        background-color: var(--color-warning-light-9, #fdf6ec);\r\n\r\n        &:hover {\r\n          background-color: var(--color-warning-light-8, #faecd8);\r\n        }"}, {"original": "#e6a23c);\r\n        background-color: var(--color-warning-light-9", "type": "id", "names": ["e6a23c"], "fullRule": "\r\n        border-left: 3px solid var(--color-warning, #e6a23c);\r\n        background-color: var(--color-warning-light-9, #fdf6ec);\r\n\r\n        &:hover {\r\n          background-color: var(--color-warning-light-8, #faecd8);\r\n        }"}, {"original": "#fdf6ec);\r\n\r\n        &:hover", "type": "id", "names": ["fdf6ec"], "fullRule": "\r\n        border-left: 3px solid var(--color-warning, #e6a23c);\r\n        background-color: var(--color-warning-light-9, #fdf6ec);\r\n\r\n        &:hover {\r\n          background-color: var(--color-warning-light-8, #faecd8);\r\n        }"}], "totalSelectors": 22}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\header\\header.vue", "unusedSelectors": [{"original": "body", "type": "tag", "names": ["body"], "fullRule": "\r\n\r\nbody {\r\n  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, SimSun, sans-serif;\r\n  margin: 0;\r\n  padding: 0;\r\n  width: 100%;\r\n  height: 100%;\r\n  overflow-x: hidden;\r\n}"}, {"original": ".add-tab-btn", "type": "class", "names": ["add-tab-btn"], "fullRule": "\r\n\r\n.add-tab-btn {\r\n  margin-left: 4px;\r\n  padding: 0;\r\n  width: 24px;\r\n  height: 24px;\r\n  border: none;\r\n  background: transparent;\r\n  color: var(--white);\r\n  cursor: pointer;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  font-size: 21px;\r\n  transition: all 0.2s;\r\n  -webkit-app-region: no-drag;\r\n}"}, {"original": ".add-tab-btn:focus", "type": "class", "names": ["add-tab-btn"], "fullRule": "\r\n\r\n.add-tab-btn:focus {\r\n  outline: none;\r\n  box-shadow: none;\r\n}"}, {"original": ".add-tab-btn:hover", "type": "class", "names": ["add-tab-btn"], "fullRule": "\r\n\r\n.add-tab-btn:hover {\r\n  background: var(--tab-hover-bg);\r\n}"}, {"original": ".right", "type": "class", "names": ["right"], "fullRule": "\r\n\r\n.right {\r\n  height: 100%;\r\n  display: flex;\r\n  align-items: center;\r\n  padding-right: 15px;\r\n}"}, {"original": ".navigation-control", "type": "class", "names": ["navigation-control"], "fullRule": "\r\n\r\n.navigation-control {\r\n  display: flex;\r\n  align-items: center;\r\n  margin-right: 8px;\r\n  border-right: 1px solid rgba(255, 255, 255, 0.15);\r\n  padding-right: 8px;\r\n  -webkit-app-region: no-drag;\r\n}"}, {"original": ".navigation-control i", "type": "class", "names": ["navigation-control"], "fullRule": "\r\n\r\n.navigation-control i {\r\n  width: 32px;\r\n  height: 28px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  -webkit-app-region: no-drag;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  border-radius: 3px;\r\n  margin: 0 1px;\r\n}"}, {"original": ".navigation-control i:hover", "type": "class", "names": ["navigation-control"], "fullRule": "\r\n\r\n.navigation-control i:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}"}, {"original": ".language-btn", "type": "class", "names": ["language-btn"], "fullRule": "\r\n.language-btn {\r\n  width: 42px;\r\n  height: 28px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  -webkit-app-region: no-drag;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n  border-radius: 3px;\r\n  margin: 0 1px;\r\n  font-size: 11px;\r\n  color: var(--white);\r\n}"}, {"original": ".language-btn:hover", "type": "class", "names": ["language-btn"], "fullRule": "\r\n\r\n.language-btn:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}"}, {"original": ".language-text", "type": "class", "names": ["language-text"], "fullRule": "\r\n\r\n.language-text {\r\n  font-size: 10px;\r\n  font-weight: 500;\r\n}"}, {"original": ".window-control", "type": "class", "names": ["window-control"], "fullRule": "\r\n\r\n.window-control {\r\n  display: flex;\r\n  align-items: center;\r\n}"}, {"original": ".window-control i", "type": "class", "names": ["window-control"], "fullRule": "\r\n\r\n.window-control i {\r\n  width: 46px;\r\n  height: 35px;\r\n  font-size: 12px;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  -webkit-app-region: no-drag;\r\n  cursor: pointer;\r\n  transition: background-color 0.2s;\r\n}"}, {"original": ".window-control i:hover", "type": "class", "names": ["window-control"], "fullRule": "\r\n\r\n.window-control i:hover {\r\n  background-color: rgba(255, 255, 255, 0.1);\r\n}"}], "totalSelectors": 32}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\layout\\header.vue", "unusedSelectors": [{"original": "display: flex;\r\n    align-items: center;\r\n    flex: 1;\r\n    height: 100%;\r\n\r\n    .el-menu", "type": "class", "names": ["el-menu"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    flex: 1;\r\n    height: 100%;\r\n\r\n    .el-menu {\r\n      flex: 1;\r\n    }"}, {"original": "width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        font-size: 20px;\r\n        border-radius: 50%;\r\n\r\n        &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        font-size: 20px;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n          background: var(--gray-600);\r\n        }"}, {"original": "width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        font-size: 16px;\r\n        border-radius: 50%;\r\n\r\n        &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        font-size: 16px;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n          background: var(--gray-600);\r\n        }"}, {"original": "width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        \r\n        &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n        width: 30px;\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        cursor: pointer;\r\n        \r\n        &:hover {\r\n          background: var(--gray-600);\r\n        }"}, {"original": "&.close:hover", "type": "class", "names": ["close"], "fullRule": "\r\n\r\n        &.close:hover {\r\n          background: #e81123;\r\n        }"}, {"original": ".process", "type": "class", "names": ["process"], "fullRule": "\r\n\r\n    .process {\r\n      margin-right: 10px;\r\n    }"}], "totalSelectors": 7}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\login\\login.vue", "unusedSelectors": [{"original": "margin-top: 25px;\r\n        margin-left: 10%;\r\n\r\n        &>li", "type": "tag", "names": ["margin"], "fullRule": "\r\n        margin-top: 25px;\r\n        margin-left: 10%;\r\n\r\n        &>li {\r\n          margin-bottom: 10px;\r\n          font-size: 15px;\r\n        }"}, {"original": ".el-carousel__item", "type": "class", "names": ["el-carousel__item"], "fullRule": "\r\n\r\n      .el-carousel__item,\r\n      .item-wrap {\r\n        height: 340px;\r\n      }"}, {"original": ".item-wrap", "type": "class", "names": ["item-wrap"], "fullRule": "\r\n\r\n      .el-carousel__item,\r\n      .item-wrap {\r\n        height: 340px;\r\n      }"}, {"original": "flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 0 40px;\r\n      position: relative;\r\n      height: 100%;\r\n\r\n      &>h2", "type": "tag", "names": ["flex"], "fullRule": "\r\n      flex: 1;\r\n      display: flex;\r\n      flex-direction: column;\r\n      padding: 0 40px;\r\n      position: relative;\r\n      height: 100%;\r\n\r\n      &>h2 {\r\n        margin-top: 20px;\r\n      }"}, {"original": "display: flex;\r\n\r\n        &>img", "type": "tag", "names": ["display"], "fullRule": "\r\n        display: flex;\r\n\r\n        &>img {\r\n          width: 200px;\r\n          height: 40px;\r\n        }"}], "totalSelectors": 8}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\banner\\banner.vue", "unusedSelectors": [{"original": ">.el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n    >.el-tree-node__content {\r\n      background: #b3d6fd;\r\n    }"}, {"original": ".el-tree__drop-indicator", "type": "class", "names": ["el-tree__drop-indicator"], "fullRule": "\r\n\r\n  .el-tree__drop-indicator {\r\n    height: 3px;\r\n  }"}, {"original": ".ws-icon", "type": "class", "names": ["ws-icon"], "fullRule": "\r\n    .ws-icon {\r\n      font-size: 14px;\r\n      margin-right: 5px;\r\n      color: var(--red);\r\n    }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n    .folder-icon {\r\n      color: var(--yellow);\r\n      flex: 0 0 auto;\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-right: 5px;\r\n    }"}, {"original": "display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top", "type": "class", "names": ["node-top"], "fullRule": "\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top {\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\r\n\r\n      .node-bottom {\r\n        color: var(--gray-500);\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": "flex: 0 0 75%;\r\n      height: 22px;\r\n      border: 1px solid var(--theme-color);\r\n      font-size: 1em;\r\n      margin-left: -1px;\r\n\r\n      &.error", "type": "class", "names": ["error"], "fullRule": "\r\n      flex: 0 0 75%;\r\n      height: 22px;\r\n      border: 1px solid var(--theme-color);\r\n      font-size: 1em;\r\n      margin-left: -1px;\r\n\r\n      &.error {\r\n        border: 2px solid var(--red);\r\n      }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n      .folder-icon {\r\n        color: var(--gray-300) !important;\r\n      }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n      .folder-icon {\r\n        color: var(--gray-500) !important;\r\n      }"}, {"original": "// 禁用动画提高性能\r\n  .el-collapse-transition-enter-active", "type": "class", "names": ["el-collapse-transition-enter-active"], "fullRule": "\r\n\r\n  // 禁用动画提高性能\r\n  .el-collapse-transition-enter-active,\r\n  .el-collapse-transition-leave-active {\r\n    transition: none !important;\r\n  }"}, {"original": ".el-collapse-transition-leave-active", "type": "class", "names": ["el-collapse-transition-leave-active"], "fullRule": "\r\n\r\n  // 禁用动画提高性能\r\n  .el-collapse-transition-enter-active,\r\n  .el-collapse-transition-leave-active {\r\n    transition: none !important;\r\n  }"}, {"original": "align-items: flex-start;\r\n\r\n      &>.el-tree-node__expand-icon", "type": "class", "names": ["el-tree-node__expand-icon"], "fullRule": "\r\n      align-items: flex-start;\r\n\r\n      &>.el-tree-node__expand-icon {\r\n        padding-top: 4px;\r\n      }"}, {"original": ".el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n\r\n  .el-tree-node__content {\r\n    height: auto;\r\n    display: flex;\r\n    align-items: center;\r\n  }"}, {"original": ".el-tree-node__content>.el-tree-node__expand-icon", "type": "class", "names": ["el-tree-node__content", "el-tree-node__expand-icon"], "fullRule": "\r\n\r\n  .el-tree-node__content>.el-tree-node__expand-icon {\r\n    transition: none; //去除所有动画\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n    margin-top: -1px;\r\n  }"}, {"original": "color: #adb5bd;\r\n      cursor: default;\r\n      &:hover", "type": "id", "names": ["adb5bd"], "fullRule": "\r\n      color: #adb5bd;\r\n      cursor: default;\r\n      &:hover {\r\n        background: inherit;\r\n        color: #adb5bd;\r\n      }"}, {"original": ".hot-key", "type": "class", "names": ["hot-key"], "fullRule": "\r\n    .hot-key {\r\n      margin-left: auto;\r\n      color: #adb5bd;\r\n    }"}], "totalSelectors": 27}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\banner\\tool\\tool.vue", "unusedSelectors": [{"original": "height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex: 0 0 30px;\r\n    cursor: pointer;\r\n\r\n    &:hover", "type": "tag", "names": ["height"], "fullRule": "\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex: 0 0 30px;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: var(--theme-color);\r\n    }"}, {"original": "width: 25px;\r\n    height: 25px;\r\n    position: absolute;\r\n    top: 8px;\r\n    right: 25px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .el-badge__content", "type": "class", "names": ["el-badge__content"], "fullRule": "\r\n    width: 25px;\r\n    height: 25px;\r\n    position: absolute;\r\n    top: 8px;\r\n    right: 25px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n\r\n    .el-badge__content {\r\n      transition: none;\r\n    }"}, {"original": ".el-input__wrapper", "type": "class", "names": ["el-input__wrapper"], "fullRule": "\r\n    .el-input__wrapper {\r\n      border-radius: 20px;\r\n    }"}, {"original": "position: relative;\r\n    align-items: center;\r\n    display: flex;\r\n\r\n    .item", "type": "class", "names": ["item"], "fullRule": "\r\n    position: relative;\r\n    align-items: center;\r\n    display: flex;\r\n\r\n    .item {\r\n      outline: none;\r\n    }"}, {"original": ".operation", "type": "class", "names": ["operation"], "fullRule": "\r\n\r\n    .operation {\r\n      width: 85%;\r\n      display: flex;\r\n      justify-content: space-between;\r\n    }"}, {"original": ".more", "type": "class", "names": ["more"], "fullRule": "\r\n\r\n    .more {\r\n      display: flex;\r\n      justify-content: center;\r\n      margin-left: auto;\r\n      width: 10%;\r\n      position: relative;\r\n    }"}, {"original": "width: 25px;\r\n      height: 25px;\r\n      padding: 5px;\r\n\r\n      &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n      width: 25px;\r\n      height: 25px;\r\n      padding: 5px;\r\n\r\n      &:hover {\r\n        background: var(--gray-400);\r\n      }"}, {"original": "width: 25px;\r\n    height: 25px;\r\n    line-height: 25px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n\r\n    &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n    width: 25px;\r\n    height: 25px;\r\n    line-height: 25px;\r\n    text-align: center;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      background: var(--gray-400);\r\n    }"}, {"original": "height: 40px;\r\n  width: 100%;\r\n  padding: 0 10px 0 20px;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  // cursor: default;\r\n  .label", "type": "class", "names": ["label"], "fullRule": "\r\n  height: 40px;\r\n  width: 100%;\r\n  padding: 0 10px 0 20px;\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  // cursor: default;\r\n  .label {\r\n    width: 120px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    cursor: pointer;\r\n  }"}, {"original": ".shortcut", "type": "class", "names": ["shortcut"], "fullRule": "\r\n\r\n  .shortcut {\r\n    width: 100px;\r\n    color: var(--gray-500);\r\n  }"}, {"original": ".svg-icon", "type": "class", "names": ["svg-icon"], "fullRule": "\r\n\r\n  .svg-icon {\r\n    width: 25px;\r\n    height: 25px;\r\n    padding: 5px;\r\n  }"}, {"original": "color: var(--theme-color);\r\n\r\n      &:hover", "type": "tag", "names": ["color"], "fullRule": "\r\n      color: var(--theme-color);\r\n\r\n      &:hover {\r\n        color: var(--theme-color);\r\n      }"}, {"original": "display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n  right: 5px;\r\n  top: 5px;\r\n  font-size: 18px;\r\n  width: 22px;\r\n  height: 22px;\r\n  color: #f56c6c;\r\n  cursor: pointer;\r\n  border-radius: 50%;\r\n  &:hover", "type": "id", "names": ["f56c6c"], "fullRule": "\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  position: absolute;\r\n  right: 5px;\r\n  top: 5px;\r\n  font-size: 18px;\r\n  width: 22px;\r\n  height: 22px;\r\n  color: #f56c6c;\r\n  cursor: pointer;\r\n  border-radius: 50%;\r\n  &:hover {\r\n    background: #dee2e6;\r\n  }"}, {"original": "flex: 0 0 auto;\r\n\r\n  .el-checkbox", "type": "class", "names": ["el-checkbox"], "fullRule": "\r\n  flex: 0 0 auto;\r\n\r\n  .el-checkbox,\r\n  .el-radio {\r\n    margin-right: 15px;\r\n  }"}, {"original": ".el-radio", "type": "class", "names": ["el-radio"], "fullRule": "\r\n  flex: 0 0 auto;\r\n\r\n  .el-checkbox,\r\n  .el-radio {\r\n    margin-right: 15px;\r\n  }"}, {"original": ".el-checkbox-group", "type": "class", "names": ["el-checkbox-group"], "fullRule": "\r\n\r\n  .el-checkbox-group {\r\n    display: flex;\r\n  }"}, {"original": "min-height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    &:not(:last-of-type)", "type": "tag", "names": ["min"], "fullRule": "\r\n    min-height: 40px;\r\n    display: flex;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    &:not(:last-of-type) {\r\n      border-bottom: 1px dashed var(--gray-300);\r\n    }"}, {"original": ".el-button--text", "type": "class", "names": ["el-button--text"], "fullRule": "\r\n\r\n    .el-button--text {\r\n      padding-top: 5px;\r\n      padding-bottom: 5px;\r\n    }"}, {"original": ".el-radio-group", "type": "class", "names": ["el-radio-group"], "fullRule": "\r\n\r\n    .el-radio-group {\r\n      display: flex;\r\n      align-items: center;\r\n    }"}, {"original": "min-height: 300px;\r\n\r\n  h3", "type": "tag", "names": ["min"], "fullRule": "\r\n  min-height: 300px;\r\n\r\n  h3 {\r\n    margin-top: 5px;\r\n    margin-bottom: 5px;\r\n  }"}, {"original": ".project-wrap", "type": "class", "names": ["project-wrap"], "fullRule": "\r\n\r\n  .project-wrap {\r\n    padding: 0 10px 0 20px;\r\n    max-height: 300px;\r\n    overflow-y: auto;\r\n  }"}, {"original": "height: 35px;\r\n    padding: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .item-title", "type": "class", "names": ["item-title"], "fullRule": "\r\n    height: 35px;\r\n    padding: 10px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n\r\n    .item-title {\r\n      flex: 0 0 75%;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      margin-right: 25px;\r\n    }"}, {"original": "background-color: var(--theme-color);\r\n      color: var(--white);\r\n      cursor: pointer;\r\n\r\n      .item-content", "type": "class", "names": ["item-content"], "fullRule": "\r\n      background-color: var(--theme-color);\r\n      color: var(--white);\r\n      cursor: pointer;\r\n\r\n      .item-content {\r\n        color: var(--white);\r\n      }"}], "totalSelectors": 24}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\apidoc.vue", "unusedSelectors": [{"original": "flex-direction: column;\r\n    overflow: hidden;\r\n\r\n    .el-divider--horizontal", "type": "class", "names": ["el-divider--horizontal"], "fullRule": "\r\n    flex-direction: column;\r\n    overflow: hidden;\r\n\r\n    .el-divider--horizontal {\r\n      border-top: 1px dashed var(--gray-500);\r\n    }"}, {"original": ".el-divider--horizontal", "type": "class", "names": ["el-divider--horizontal"], "fullRule": "\r\n\r\n  .el-divider--horizontal {\r\n    margin: 0;\r\n    z-index: var(--zIndex-drag-bar);\r\n    font-size: 14px;\r\n  }"}], "totalSelectors": 4}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\dialog\\curd-host\\curd-host.vue", "unusedSelectors": [{"original": ".url-wrap", "type": "class", "names": ["url-wrap"], "fullRule": "\r\n\r\n  .url-wrap {\r\n    height: 45px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\operation\\operation.vue", "unusedSelectors": [{"original": "position: sticky;\r\n  top: 0;\r\n  padding: 10px 20px;\r\n  box-shadow: 0 3px 2px var(--gray-400);\r\n  background: var(--white);\r\n  z-index: var(--zIndex-request-info-wrap);\r\n  height: var(--apiflow-apidoc-operation-height);\r\n\r\n  &.prefix", "type": "class", "names": ["prefix"], "fullRule": "\r\n  position: sticky;\r\n  top: 0;\r\n  padding: 10px 20px;\r\n  box-shadow: 0 3px 2px var(--gray-400);\r\n  background: var(--white);\r\n  z-index: var(--zIndex-request-info-wrap);\r\n  height: var(--apiflow-apidoc-operation-height);\r\n\r\n  &.prefix {\r\n    height: 130px;\r\n  }"}, {"original": ".proxy-wrap", "type": "class", "names": ["proxy-wrap"], "fullRule": "\r\n\r\n  .proxy-wrap {\r\n    margin-left: auto;\r\n  }"}, {"original": "display: flex;\r\n    margin-top: 10px;\r\n\r\n    :deep(.el-input__inner)", "type": "class", "names": ["el-input__inner"], "fullRule": "\r\n    display: flex;\r\n    margin-top: 10px;\r\n\r\n    :deep(.el-input__inner) {\r\n      font-size: 13px;\r\n    }"}, {"original": "display: flex;\r\n      align-items: center;\r\n\r\n      :deep(.el-select)", "type": "class", "names": ["el-select"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      :deep(.el-select) {\r\n        width: 100px;\r\n      }"}, {"original": ".el-input__suffix", "type": "class", "names": ["el-input__suffix"], "fullRule": "\r\n\r\n    .el-input__suffix {\r\n      display: flex;\r\n      align-items: center;\r\n    }"}, {"original": "height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular", "type": "id", "names": ["d1d5da", "f0f0f0"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono,Courier New, monospace;\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "<PERSON><PERSON>", "type": "tag", "names": ["<PERSON><PERSON>"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono,Courier New, monospace;\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Monaco", "type": "tag", "names": ["Monaco"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono,Courier New, monospace;\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Consolas", "type": "tag", "names": ["Consolas"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono,Courier New, monospace;\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Liberation Mono", "type": "tag", "names": ["Liberation"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono,Courier New, monospace;\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Courier New", "type": "tag", "names": ["Courier"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono,Courier New, monospace;\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "monospace;\r\n    &::-webkit-scrollbar", "type": "tag", "names": ["monospace"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    white-space: nowrap;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    white-space: pre-wrap;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono,Courier New, monospace;\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "display: flex;\r\n      align-items: center;\r\n      height: 30px;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      overflow-x: auto;\r\n      &::-webkit-scrollbar", "type": "tag", "names": ["display"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      height: 30px;\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n      overflow-x: auto;\r\n      &::-webkit-scrollbar {\r\n        height: 0px;\r\n      }"}, {"original": ".tip", "type": "class", "names": ["tip"], "fullRule": "\r\n    .tip {\r\n      flex: 0 0 30px;\r\n      height: 30px;\r\n      display: flex;\r\n      align-items: center;\r\n      margin-left: 5px;\r\n    }"}, {"original": ".env-item", "type": "class", "names": ["env-item"], "fullRule": "\r\n\r\n.env-item {\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: space-between;\r\n  width: 500px;\r\n}"}], "totalSelectors": 17}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\body\\body.vue", "unusedSelectors": [{"original": ".operation", "type": "class", "names": ["operation"], "fullRule": "\r\n\r\n  .operation {\r\n    margin-top: -3px;\r\n    flex: 1;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    position: relative;\r\n  }"}, {"original": "width: 576px;\r\n      height: 194px;\r\n      position: absolute;\r\n      left: 50%;\r\n      top: 50%;\r\n      transform: translate(-50%", "type": "tag", "names": ["width"], "fullRule": "\r\n      width: 576px;\r\n      height: 194px;\r\n      position: absolute;\r\n      left: 50%;\r\n      top: 50%;\r\n      transform: translate(-50%, -50%);\r\n      user-select: none;\r\n      border: 1px solid var(--gray-400);\r\n\r\n      &>img {\r\n        opacity: 0.5;\r\n      }"}, {"original": "font-size: 16px;\r\n        cursor: pointer;\r\n        &:hover", "type": "tag", "names": ["font"], "fullRule": "\r\n        font-size: 16px;\r\n        cursor: pointer;\r\n        &:hover {\r\n          color: var(--red);\r\n        }"}, {"original": "top: 30px;\r\n    left: -200px;\r\n    background: var(--white);\r\n    z-index: var(--zIndex-contextmenu);\r\n    position: absolute;\r\n    min-width: 250px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: rgb(0 0 0 / 10%) 0px 2px 8px 0px; //墨刀弹窗样式\r\n    max-height: 220px;\r\n    overflow-y: auto;\r\n\r\n    &::-webkit-scrollbar", "type": "tag", "names": ["top"], "fullRule": "\r\n    top: 30px;\r\n    left: -200px;\r\n    background: var(--white);\r\n    z-index: var(--zIndex-contextmenu);\r\n    position: absolute;\r\n    min-width: 250px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: rgb(0 0 0 / 10%) 0px 2px 8px 0px; //墨刀弹窗样式\r\n    max-height: 220px;\r\n    overflow-y: auto;\r\n\r\n    &::-webkit-scrollbar {\r\n      width: 5px;\r\n    }"}, {"original": "border-bottom: 1px solid var(--gray-300);\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 3px 20px 3px 5px;\r\n\r\n      .el-input__inner", "type": "class", "names": ["el-input__inner"], "fullRule": "\r\n      border-bottom: 1px solid var(--gray-300);\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 3px 20px 3px 5px;\r\n\r\n      .el-input__inner {\r\n        border: none;\r\n      }"}, {"original": "&.active", "type": "class", "names": ["active"], "fullRule": "\r\n\r\n      &.active {\r\n        background: var(--theme-color);\r\n        color: var(--white);\r\n      }"}, {"original": ".head", "type": "class", "names": ["head"], "fullRule": "\r\n\r\n      .head {\r\n        margin-right: 10px;\r\n      }"}, {"original": ".tail", "type": "class", "names": ["tail"], "fullRule": "\r\n\r\n      .tail {\r\n        margin-left: auto;\r\n        // color: $gray-500;\r\n      }"}], "totalSelectors": 17}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\headers\\headers.vue", "unusedSelectors": [{"original": ".value-wrap", "type": "class", "names": ["value-wrap"], "fullRule": "\r\n  .value-wrap {\r\n    max-height: 140px;\r\n    overflow-y: auto;\r\n  }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n  .folder-icon {\r\n    color: #ffc107;\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\hook\\hook.vue", "unusedSelectors": [{"original": "padding: 5px 5px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    cursor: pointer;\r\n\r\n    &:hover", "type": "tag", "names": ["padding"], "fullRule": "\r\n    padding: 5px 5px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    cursor: pointer;\r\n\r\n    &:hover {\r\n      color: var(--white);\r\n      background-color: var(--theme-color);\r\n    }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\mock\\components\\mock-response\\mock-response.vue", "unusedSelectors": [{"original": "height: calc(100vh - 610px);\r\n    min-height: 200px;\r\n    border: 1px solid var(--gray-500);\r\n    display: flex;\r\n    position: relative;\r\n\r\n    .mock-json-editor", "type": "class", "names": ["mock-json-editor"], "fullRule": "\r\n    height: calc(100vh - 610px);\r\n    min-height: 200px;\r\n    border: 1px solid var(--gray-500);\r\n    display: flex;\r\n    position: relative;\r\n\r\n    .mock-json-editor {\r\n      height: 100%;\r\n      border-right: 1px solid var(--gray-500);\r\n    }"}, {"original": ".tip", "type": "class", "names": ["tip"], "fullRule": "\r\n\r\n    .tip {\r\n      width: 100%;\r\n      bottom: 0;\r\n      height: 25px;\r\n      display: flex;\r\n      align-items: center;\r\n      background-color: var(--orange);\r\n      color: var(--white);\r\n      position: absolute;\r\n      text-indent: 1em;\r\n    }"}, {"original": ".format-btn", "type": "class", "names": ["format-btn"], "fullRule": "\r\n\r\n    .format-btn {\r\n      position: absolute;\r\n      right: 10px;\r\n      top: 0;\r\n    }"}, {"original": ".raw-editor-wrap", "type": "class", "names": ["raw-editor-wrap"], "fullRule": "\r\n\r\n  .raw-editor-wrap {\r\n    height: calc(100vh - 610px);\r\n    min-height: 200px;\r\n  }"}, {"original": ".img-wrap", "type": "class", "names": ["img-wrap"], "fullRule": "\r\n\r\n  .img-wrap {\r\n    min-height: 200px;\r\n  }"}, {"original": ".image-demo", "type": "class", "names": ["image-demo"], "fullRule": "\r\n\r\n  .image-demo {\r\n    // position: fixed;\r\n    // left: -99999px;\r\n    // top: -99999px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    flex-direction: column;\r\n  }"}, {"original": "width: 70px;\r\n      height: 70px;\r\n      padding: 10px;\r\n      margin-right: 20px;\r\n      cursor: pointer;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      flex-direction: column;\r\n      border: 1px solid transparent;\r\n\r\n      &.active", "type": "class", "names": ["active"], "fullRule": "\r\n      width: 70px;\r\n      height: 70px;\r\n      padding: 10px;\r\n      margin-right: 20px;\r\n      cursor: pointer;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      flex-direction: column;\r\n      border: 1px solid transparent;\r\n\r\n      &.active {\r\n        border: 1px solid var(--gray-400);\r\n        box-shadow: var(--box-shadow-sm);\r\n      }"}, {"original": ".svg-icon", "type": "class", "names": ["svg-icon"], "fullRule": "\r\n\r\n      .svg-icon {\r\n        width: 40px;\r\n        height: 40px;\r\n      }"}, {"original": ".img", "type": "class", "names": ["img"], "fullRule": "\r\n\r\n      .img {\r\n        width: 28px;\r\n        height: 28px;\r\n      }"}], "totalSelectors": 10}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\mock\\mock.vue", "unusedSelectors": [{"original": ".el-tabs", "type": "class", "names": ["el-tabs"], "fullRule": "\r\n\r\n  .el-tabs {\r\n    padding: 0;\r\n  }"}], "totalSelectors": 4}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\params.vue", "unusedSelectors": [{"original": ".el-tabs", "type": "class", "names": ["el-tabs"], "fullRule": "\r\n\r\n  .el-tabs,\r\n  .workbench {\r\n    padding-right: 20px;\r\n    padding-left: 20px;\r\n  }"}, {"original": ".workbench", "type": "class", "names": ["workbench"], "fullRule": "\r\n\r\n  .el-tabs,\r\n  .workbench {\r\n    padding-right: 20px;\r\n    padding-left: 20px;\r\n  }"}, {"original": ".el-tabs__item", "type": "class", "names": ["el-tabs__item"], "fullRule": "\r\n\r\n  .el-tabs__item {\r\n    user-select: none;\r\n  }"}, {"original": "transition: none;\r\n    top: 10px;\r\n\r\n    &.is-fixed.is-dot", "type": "class", "names": ["is-fixed", "is-dot"], "fullRule": "\r\n    transition: none;\r\n    top: 10px;\r\n\r\n    &.is-fixed.is-dot {\r\n      top: 10px;\r\n      right: 3px;\r\n    }"}, {"original": ".el-tabs__item", "type": "class", "names": ["el-tabs__item"], "fullRule": "\r\n\r\n  .el-tabs__item {\r\n    height: 30px;\r\n    line-height: 30px;\r\n  }"}], "totalSelectors": 9}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\response\\children\\mime.vue", "unusedSelectors": [{"original": "padding: 1px 8px;\r\n    border: 1px solid var(--gray-400);\r\n    border-radius: 2px;\r\n    margin-right: 10px;\r\n    margin-top: 5px;\r\n    margin-bottom: 10px;\r\n    color: var(--white);\r\n    background-color: var(--theme-color);\r\n\r\n    &:hover", "type": "tag", "names": ["padding"], "fullRule": "\r\n    padding: 1px 8px;\r\n    border: 1px solid var(--gray-400);\r\n    border-radius: 2px;\r\n    margin-right: 10px;\r\n    margin-top: 5px;\r\n    margin-bottom: 10px;\r\n    color: var(--white);\r\n    background-color: var(--theme-color);\r\n\r\n    &:hover {\r\n      color: var(--white);\r\n      background-color: var(--theme-color);\r\n    }"}, {"original": "display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: absolute;\r\n    right: 5px;\r\n    top: 5px;\r\n    font-size: 18px;\r\n    width: 22px;\r\n    height: 22px;\r\n    color: #f56c6c;\r\n    cursor: pointer;\r\n    border-radius: 50%;\r\n    &:hover", "type": "id", "names": ["f56c6c"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: absolute;\r\n    right: 5px;\r\n    top: 5px;\r\n    font-size: 18px;\r\n    width: 22px;\r\n    height: 22px;\r\n    color: #f56c6c;\r\n    cursor: pointer;\r\n    border-radius: 50%;\r\n    &:hover {\r\n      background: #dee2e6;\r\n    }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\response\\children\\status.vue", "unusedSelectors": [{"original": "padding: 1px 8px;\r\n    border: 1px solid var(--gray-400);\r\n    border-radius: 2px;\r\n    margin-right: 10px;\r\n    margin-top: 5px;\r\n    margin-bottom: 10px;\r\n    color: var(--white);\r\n    background-color: var(--theme-color);\r\n\r\n    &:hover", "type": "tag", "names": ["padding"], "fullRule": "\r\n    padding: 1px 8px;\r\n    border: 1px solid var(--gray-400);\r\n    border-radius: 2px;\r\n    margin-right: 10px;\r\n    margin-top: 5px;\r\n    margin-bottom: 10px;\r\n    color: var(--white);\r\n    background-color: var(--theme-color);\r\n\r\n    &:hover {\r\n      color: var(--white);\r\n      background-color: var(--theme-color);\r\n    }"}, {"original": "display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: absolute;\r\n    right: 5px;\r\n    top: 5px;\r\n    font-size: 18px;\r\n    width: 22px;\r\n    height: 22px;\r\n    color: #f56c6c;\r\n    cursor: pointer;\r\n    border-radius: 50%;\r\n    &:hover", "type": "id", "names": ["f56c6c"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: absolute;\r\n    right: 5px;\r\n    top: 5px;\r\n    font-size: 18px;\r\n    width: 22px;\r\n    height: 22px;\r\n    color: #f56c6c;\r\n    cursor: pointer;\r\n    border-radius: 50%;\r\n    &:hover {\r\n      background: #dee2e6;\r\n    }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\params\\response\\response.vue", "unusedSelectors": [{"original": "max-width: 200px;\r\n\r\n      .type-text", "type": "class", "names": ["type-text"], "fullRule": "\r\n      max-width: 200px;\r\n\r\n      .type-text {\r\n        max-width: 200px;\r\n      }"}, {"original": ".active", "type": "class", "names": ["active"], "fullRule": "\r\n\r\n    .active {\r\n      color: var(--theme-color);\r\n    }"}, {"original": "display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    cursor: pointer;\r\n    width: 40px;\r\n    margin-top: 2px;\r\n\r\n    &:hover", "type": "tag", "names": ["display"], "fullRule": "\r\n    display: inline-flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    height: 100%;\r\n    cursor: pointer;\r\n    width: 40px;\r\n    margin-top: 2px;\r\n\r\n    &:hover {\r\n      color: var(--theme-color);\r\n    }"}, {"original": ".editor", "type": "class", "names": ["editor"], "fullRule": "\r\n\r\n    .editor {\r\n      height: 350px;\r\n    }"}, {"original": ".format-btn", "type": "class", "names": ["format-btn"], "fullRule": "\r\n\r\n    .format-btn {\r\n      position: absolute;\r\n      right: 10px;\r\n      top: 0px;\r\n    }"}, {"original": "top: 30px;\r\n    left: -200px;\r\n    background: var(--white);\r\n    z-index: var(--zIndex-contextmenu);\r\n    position: absolute;\r\n    min-width: 250px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: rgb(0 0 0 / 10%) 0px 2px 8px 0px; //墨刀弹窗样式\r\n    max-height: 220px;\r\n    overflow-y: auto;\r\n\r\n    &::-webkit-scrollbar", "type": "tag", "names": ["top"], "fullRule": "\r\n    top: 30px;\r\n    left: -200px;\r\n    background: var(--white);\r\n    z-index: var(--zIndex-contextmenu);\r\n    position: absolute;\r\n    min-width: 250px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: rgb(0 0 0 / 10%) 0px 2px 8px 0px; //墨刀弹窗样式\r\n    max-height: 220px;\r\n    overflow-y: auto;\r\n\r\n    &::-webkit-scrollbar {\r\n      width: 5px;\r\n    }"}, {"original": ".header", "type": "class", "names": ["header"], "fullRule": "\r\n\r\n    .header {\r\n      border-bottom: 1px solid var(--gray-300);\r\n      display: flex;\r\n      align-items: center;\r\n      padding: 3px 20px 3px 5px;\r\n    }"}, {"original": ".el-input__inner", "type": "class", "names": ["el-input__inner"], "fullRule": "\r\n\r\n    .el-input__inner {\r\n      border: none;\r\n      box-shadow: none;\r\n    }"}, {"original": "&.active", "type": "class", "names": ["active"], "fullRule": "\r\n\r\n      &.active {\r\n        background: var(--theme-color);\r\n        color: var(--white);\r\n      }"}, {"original": "&.disabled", "type": "class", "names": ["disabled"], "fullRule": "\r\n\r\n      &.disabled {\r\n        background: inherit;\r\n        color: inherit;\r\n      }"}, {"original": ".tail", "type": "class", "names": ["tail"], "fullRule": "\r\n\r\n      .tail {\r\n        margin-left: auto;\r\n        // color: var(--gray-500);\r\n      }"}], "totalSelectors": 17}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\response\\base-info\\base-info.vue", "unusedSelectors": [{"original": "flex-grow: 0;\r\n  flex-shrink: 0;\r\n  box-shadow: 0 3px 2px var(--gray-400);\r\n  margin-bottom: 10px;\r\n  padding: 10px;\r\n  height: var(--apiflow-apidoc-request-view-height);\r\n  overflow: hidden;\r\n\r\n  .svg-icon", "type": "class", "names": ["svg-icon"], "fullRule": "\r\n  flex-grow: 0;\r\n  flex-shrink: 0;\r\n  box-shadow: 0 3px 2px var(--gray-400);\r\n  margin-bottom: 10px;\r\n  padding: 10px;\r\n  height: var(--apiflow-apidoc-request-view-height);\r\n  overflow: hidden;\r\n\r\n  .svg-icon {\r\n    width: 15px;\r\n    height: 15px;\r\n    cursor: pointer;\r\n  }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\response\\body\\body.vue", "unusedSelectors": [{"original": ".apply-response", "type": "class", "names": ["apply-response"], "fullRule": "\r\n  .apply-response {\r\n    position: absolute;\r\n    cursor: pointer;\r\n    right: 15px;\r\n    top: 0px;\r\n    z-index: var(--zIndex-contextmenu);\r\n  }"}, {"original": "height: 100%;\r\n    .text-tool", "type": "class", "names": ["text-tool"], "fullRule": "\r\n    height: 100%;\r\n    .text-tool {\r\n      display: flex;\r\n      align-items: center;\r\n      height: 20px;\r\n      border-bottom: 1px solid var(--gray-200);\r\n    }"}, {"original": ".operation", "type": "class", "names": ["operation"], "fullRule": "\r\n  .operation {\r\n    height: 30px;\r\n    padding: 0 20px;\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: flex-end;\r\n    color: var(--gray-300);\r\n  }"}, {"original": ".process", "type": "class", "names": ["process"], "fullRule": "\r\n  .process {\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: var(--gray-600);\r\n  }"}], "totalSelectors": 17}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\response\\request\\request.vue", "unusedSelectors": [{"original": ".body-wrap", "type": "class", "names": ["body-wrap"], "fullRule": "\r\n  .body-wrap {\r\n    height: 200px;\r\n  }"}, {"original": "margin-left: 25px;\r\n    .download", "type": "class", "names": ["download"], "fullRule": "\r\n    margin-left: 25px;\r\n    .download {\r\n      margin-top: -14px;\r\n    }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\response\\res-info\\res-info.vue", "unusedSelectors": [{"original": "overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n\r\n  .content-type", "type": "class", "names": ["content-type"], "fullRule": "\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n\r\n  .content-type {\r\n    color: var(--orange);\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\response\\response.vue", "unusedSelectors": [{"original": "height: calc(100vh - var(--apiflow-apidoc-request-view-height) - var(--apiflow-doc-nav-height) - 30px);\r\n  overflow-y: auto;\r\n  \r\n  :deep(.el-tabs__header)", "type": "class", "names": ["el-tabs__header"], "fullRule": "\r\n  height: calc(100vh - var(--apiflow-apidoc-request-view-height) - var(--apiflow-doc-nav-height) - 30px);\r\n  overflow-y: auto;\r\n  \r\n  :deep(.el-tabs__header) {\r\n    margin-bottom: 0px;\r\n  }"}], "totalSelectors": 4}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apidoc\\view\\view.vue", "unusedSelectors": [{"original": ".url", "type": "class", "names": ["url"], "fullRule": "\r\n\r\n  .url {\r\n    font-size: 16px;\r\n  }"}, {"original": ".view-block", "type": "class", "names": ["view-block"], "fullRule": "\r\n\r\n  .view-block {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    color: var(--gray-700);\r\n  }"}, {"original": ".title", "type": "class", "names": ["title"], "fullRule": "\r\n\r\n  .title {\r\n    font-size: 14px;\r\n    color: var(--gray-600);\r\n    padding: 5px 0;\r\n  }"}, {"original": ".remark", "type": "class", "names": ["remark"], "fullRule": "\r\n\r\n  .remark {\r\n    white-space: pre;\r\n  }"}, {"original": ".api-doc-subtitle", "type": "class", "names": ["api-doc-subtitle"], "fullRule": "\r\n\r\n  .api-doc-subtitle {\r\n    font-size: 20px;\r\n    font-weight: bold;\r\n    color: var(--gray-700);\r\n    margin-bottom: 10px;\r\n  }"}, {"original": ".api-doc-method", "type": "class", "names": ["api-doc-method"], "fullRule": "\r\n\r\n  .api-doc-method {\r\n    font-size: 16px;\r\n    font-weight: bold;\r\n    color: var(--primary);\r\n    margin-bottom: 10px;\r\n  }"}, {"original": ".api-doc-url", "type": "class", "names": ["api-doc-url"], "fullRule": "\r\n\r\n  .api-doc-url {\r\n    font-size: 18px;\r\n    font-weight: bold;\r\n    color: var(--gray-600);\r\n    margin-bottom: 10px;\r\n  }"}, {"original": ".api-doc-description", "type": "class", "names": ["api-doc-description"], "fullRule": "\r\n\r\n  .api-doc-description {\r\n    font-size: 14px;\r\n    color: var(--gray-600);\r\n    margin-bottom: 10px;\r\n    padding: 5px 0;\r\n  }"}], "totalSelectors": 10}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apiflow\\components\\node\\node.vue", "unusedSelectors": [{"original": ".create-line-dot", "type": "class", "names": ["create-line-dot"], "fullRule": "\r\n    .create-line-dot {\r\n        border-radius: 50%;\r\n        border: 1px solid var(--theme-color);\r\n        position: absolute;\r\n        background-color: var(--white);\r\n        z-index: 2;\r\n    }"}, {"original": ".resize-border", "type": "class", "names": ["resize-border"], "fullRule": "\r\n    .resize-border {\r\n        position: absolute;\r\n        left: 0;\r\n        top: 0;\r\n        border: 1px solid var(--theme-color);\r\n        width: 100%;\r\n        height: 100%;\r\n    }"}, {"original": ".position-info", "type": "class", "names": ["position-info"], "fullRule": "\r\n    .position-info {\r\n        position: absolute;\r\n        left: 50%;\r\n        transform: translate(-50%, 0);\r\n        white-space: nowrap;\r\n        bottom: -35px;\r\n        padding: 5px 10px;\r\n        border: 1px solid #d0d0d0;\r\n        background-color: #f2f2f2;\r\n    }"}, {"original": "flex: 0 0 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        border-bottom: 1px solid rgba(0", "type": "tag", "names": ["flex"], "fullRule": "\r\n        flex: 0 0 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        border-bottom: 1px solid rgba(0, 0, 0, 0.12);\r\n        .title {\r\n          width: 80%;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n          text-overflow: ellipsis;\r\n          font-weight: bolder;\r\n          font-size: 13px;\r\n          text-indent: 1em;\r\n          user-select: none;\r\n        }"}, {"original": "0.12);\r\n        .title", "type": "class", "names": ["title"], "fullRule": "\r\n        flex: 0 0 40px;\r\n        display: flex;\r\n        align-items: center;\r\n        border-bottom: 1px solid rgba(0, 0, 0, 0.12);\r\n        .title {\r\n          width: 80%;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n          text-overflow: ellipsis;\r\n          font-weight: bolder;\r\n          font-size: 13px;\r\n          text-indent: 1em;\r\n          user-select: none;\r\n        }"}, {"original": "width: 25px;\r\n            height: 25px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 5px;\r\n            cursor: pointer;\r\n            &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n            width: 25px;\r\n            height: 25px;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            margin-right: 5px;\r\n            cursor: pointer;\r\n            &:hover {\r\n              background-color: rgba(0, 0, 0, 0.12);\r\n            }"}, {"original": "height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 13px;\r\n        padding: 0 10px;\r\n        width: 100%;\r\n        .method", "type": "class", "names": ["method"], "fullRule": "\r\n        height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        font-size: 13px;\r\n        padding: 0 10px;\r\n        width: 100%;\r\n        .method {\r\n          color: #f90;\r\n        }"}, {"original": ".url", "type": "class", "names": ["url"], "fullRule": "\r\n        .url {\r\n          font-size: 14px;\r\n          overflow: hidden;\r\n          white-space: nowrap;\r\n          text-overflow: ellipsis;\r\n        }"}, {"original": ".content", "type": "class", "names": ["content"], "fullRule": "\r\n\r\n      .content {\r\n        flex: 1;\r\n      }"}, {"original": ".empty", "type": "class", "names": ["empty"], "fullRule": "\r\n\r\n      .empty {\r\n        flex: 1;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n      }"}], "totalSelectors": 11}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apiflow\\components\\selection\\selected-node-area.vue", "unusedSelectors": [{"original": "border: 1px solid var(--theme-color);\r\n        position: absolute;\r\n        background-color: var(--white);\r\n        &:hover", "type": "tag", "names": ["border"], "fullRule": "\r\n        border: 1px solid var(--theme-color);\r\n        position: absolute;\r\n        background-color: var(--white);\r\n        &:hover{\r\n            background-color: var(--theme-color);\r\n        }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\apiflow\\components\\tools\\tools.vue", "unusedSelectors": [{"original": "color: var(--gray-300);\r\n            cursor: default;\r\n            &:hover", "type": "tag", "names": ["color"], "fullRule": "\r\n            color: var(--gray-300);\r\n            cursor: default;\r\n            &:hover {\r\n                background-color: inherit;\r\n            }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\config\\config.vue", "unusedSelectors": [{"original": "height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      position: relative;\r\n      font-size: 12px;\r\n      flex: 0 0 auto;\r\n      width: 200px;\r\n      cursor: default;\r\n      padding: 0 10px;\r\n      .item-text", "type": "class", "names": ["item-text"], "fullRule": "\r\n      height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n      position: relative;\r\n      font-size: 12px;\r\n      flex: 0 0 auto;\r\n      width: 200px;\r\n      cursor: default;\r\n      padding: 0 10px;\r\n      .item-text {\r\n        margin-left: 5px;\r\n        display: inline-block;\r\n        max-width: 100px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": "background: rgb(222", "type": "tag", "names": ["background"], "fullRule": "\r\n      background: rgb(222, 225, 230);\r\n      &:hover {\r\n        background: #e2e2e2;\r\n      }"}, {"original": ".iconfont", "type": "class", "names": ["iconfont"], "fullRule": "\r\n      .iconfont {\r\n        font-size: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n      }"}, {"original": "&.active", "type": "class", "names": ["active"], "fullRule": "\r\n      &.active {\r\n        background: #f0f3fa;\r\n      }"}, {"original": ".el-form-item", "type": "class", "names": ["el-form-item"], "fullRule": "\r\n\r\n    .el-form-item {\r\n      margin-bottom: 0;\r\n    }"}, {"original": ".el-form-item", "type": "class", "names": ["el-form-item"], "fullRule": "\r\n      .el-form-item {\r\n        border-bottom: 1px solid var(--gray-200);\r\n      }"}], "totalSelectors": 6}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\cookies\\cookies.vue", "unusedSelectors": [{"original": "margin: 0 auto;\r\n  padding: 16px 0;\r\n  font-size: 22px;\r\n  .expire-tip", "type": "class", "names": ["expire-tip"], "fullRule": "\r\n  margin: 0 auto;\r\n  padding: 16px 0;\r\n  font-size: 22px;\r\n  .expire-tip {\r\n    border-bottom: 1px dashed var(--gray-500);\r\n    cursor: pointer;\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\export\\export.vue", "unusedSelectors": [{"original": ".more", "type": "class", "names": ["more"], "fullRule": "\r\n                .more {\r\n                    display: block;\r\n                }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n            .folder-icon {\r\n                color: var(--yellow);\r\n                flex: 0 0 auto;\r\n                width: 16px;\r\n                height: 16px;\r\n                margin-right: 5px;\r\n            }"}, {"original": "display: flex;\r\n                flex-direction: column;\r\n                flex: 1;\r\n                overflow: hidden;\r\n                .node-top", "type": "class", "names": ["node-top"], "fullRule": "\r\n                display: flex;\r\n                flex-direction: column;\r\n                flex: 1;\r\n                overflow: hidden;\r\n                .node-top {\r\n                    width: 100%;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    white-space: nowrap;\r\n                }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\r\n                .node-bottom {\r\n                    color: var(--gray-500);\r\n                    width: 100%;\r\n                    overflow: hidden;\r\n                    text-overflow: ellipsis;\r\n                    white-space: nowrap;\r\n                }"}, {"original": ":deep(.el-tree-node__content)", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n        :deep(.el-tree-node__content) {\r\n            height: 30px;\r\n            display: flex;\r\n            align-items: center;\r\n        }"}, {"original": ":deep(.el-tree-node__content>.el-tree-node__expand-icon)", "type": "class", "names": ["el-tree-node__content", "el-tree-node__expand-icon"], "fullRule": "\r\n        :deep(.el-tree-node__content>.el-tree-node__expand-icon) {\r\n            transition: none; //去除所有动画\r\n            padding-top: 0;\r\n            padding-bottom: 0;\r\n            margin-top: -1px;\r\n        }"}], "totalSelectors": 10}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\export\\fork\\fork.vue", "unusedSelectors": [{"original": ".el-divider--horizontal", "type": "class", "names": ["el-divider--horizontal"], "fullRule": "\r\n  .el-divider--horizontal {\r\n    margin: 10px 0;\r\n  }"}, {"original": ".right", "type": "class", "names": ["right"], "fullRule": "\r\n\r\n    .right {\r\n      flex: 1;\r\n      padding: 0 15px;\r\n      border-bottom: 1px solid var(--gray-200);\r\n    }"}, {"original": ".el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n\r\n    .el-tree-node__content {\r\n      height: 30px;\r\n      display: flex;\r\n      align-items: center;\r\n    }"}, {"original": ".more", "type": "class", "names": ["more"], "fullRule": "\r\n        .more {\r\n          display: block;\r\n        }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n      .folder-icon {\r\n        color: var(--yellow);\r\n        flex: 0 0 auto;\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-right: 5px;\r\n      }"}, {"original": "display: flex;\r\n        flex-direction: column;\r\n        flex: 1;\r\n        overflow: hidden;\r\n\r\n        .node-top", "type": "class", "names": ["node-top"], "fullRule": "\r\n        display: flex;\r\n        flex-direction: column;\r\n        flex: 1;\r\n        overflow: hidden;\r\n\r\n        .node-top {\r\n          width: 100%;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\r\n\r\n        .node-bottom {\r\n          color: var(--gray-500);\r\n          width: 100%;\r\n          overflow: hidden;\r\n          text-overflow: ellipsis;\r\n          white-space: nowrap;\r\n        }"}, {"original": ".el-tree__drop-indicator", "type": "class", "names": ["el-tree__drop-indicator"], "fullRule": "\r\n\r\n  .el-tree__drop-indicator {\r\n    height: 3px;\r\n  }"}], "totalSelectors": 11}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\history\\history.vue", "unusedSelectors": [{"original": "min-height: 50px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      &:not(:last-of-type)", "type": "tag", "names": ["min"], "fullRule": "\r\n      min-height: 50px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      &:not(:last-of-type) {\r\n        border-bottom: 1px dashed var(--gray-300);\r\n      }"}, {"original": ".el-button--text", "type": "class", "names": ["el-button--text"], "fullRule": "\r\n\r\n      .el-button--text {\r\n        padding-top: 5px;\r\n        padding-bottom: 5px;\r\n      }"}, {"original": "display: inline-flex;\r\n        max-width: 30%;\r\n        overflow-x: auto;\r\n\r\n        &::-webkit-scrollbar", "type": "tag", "names": ["display"], "fullRule": "\r\n        display: inline-flex;\r\n        max-width: 30%;\r\n        overflow-x: auto;\r\n\r\n        &::-webkit-scrollbar {\r\n          height: 0px;\r\n        }"}, {"original": "flex: 0 0 auto;\r\n          display: inline-flex;\r\n          align-items: center;\r\n          height: 25px;\r\n          padding: 2px 10px;\r\n          border: 1px solid var(--gray-300);\r\n\r\n          &:not(:last-child)", "type": "tag", "names": ["flex"], "fullRule": "\r\n          flex: 0 0 auto;\r\n          display: inline-flex;\r\n          align-items: center;\r\n          height: 25px;\r\n          padding: 2px 10px;\r\n          border: 1px solid var(--gray-300);\r\n\r\n          &:not(:last-child) {\r\n            margin-right: 10px;\r\n          }"}], "totalSelectors": 9}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\import\\import.vue", "unusedSelectors": [{"original": ".el-upload-dragger", "type": "class", "names": ["el-upload-dragger"], "fullRule": "\r\n\r\n  .el-upload-dragger {\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    width: 100%;\r\n  }"}, {"original": ".more", "type": "class", "names": ["more"], "fullRule": "\r\n      .more {\r\n        display: block;\r\n      }"}, {"original": ".file-icon", "type": "class", "names": ["file-icon"], "fullRule": "\r\n\r\n    .file-icon {\r\n      font-size: 14px;\r\n      margin-right: 5px;\r\n    }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n    .folder-icon {\r\n      color: var(--yellow);\r\n      flex: 0 0 auto;\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-right: 5px;\r\n    }"}, {"original": "display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top", "type": "class", "names": ["node-top"], "fullRule": "\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top {\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\r\n\r\n      .node-bottom {\r\n        color: var(--gray-500);\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": ".el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n\r\n  .el-tree-node__content {\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n  }"}, {"original": ".el-tree-node__content>.el-tree-node__expand-icon", "type": "class", "names": ["el-tree-node__content", "el-tree-node__expand-icon"], "fullRule": "\r\n\r\n  .el-tree-node__content>.el-tree-node__expand-icon {\r\n    transition: none; //去除所有动画\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n    margin-top: -1px;\r\n  }"}], "totalSelectors": 9}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\link\\dialog\\add.vue", "unusedSelectors": [{"original": "height: 28px;\n    white-space: nowrap;\n    overflow-y: auto;\n    user-select: auto;\n\n    &::-webkit-scrollbar", "type": "tag", "names": ["height"], "fullRule": "\n    height: 28px;\n    white-space: nowrap;\n    overflow-y: auto;\n    user-select: auto;\n\n    &::-webkit-scrollbar {\n      height: 0px;\n    }"}, {"original": ".link-icon", "type": "class", "names": ["link-icon"], "fullRule": "\n\n  .link-icon {\n    width: 120px;\n    height: 120px;\n  }"}, {"original": ":deep(.el-tree-node__content)", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\n  :deep(.el-tree-node__content) {\n    height: 30px;\n  }"}, {"original": ".more", "type": "class", "names": ["more"], "fullRule": "\n      .more {\n        display: block;\n      }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\n    .folder-icon {\n      color: #ffc107;\n      flex: 0 0 auto;\n      width: 16px;\n      height: 16px;\n      margin-right: 5px;\n    }"}, {"original": "display: flex;\n      flex-direction: column;\n      flex: 1;\n      overflow: hidden;\n      .node-top", "type": "class", "names": ["node-top"], "fullRule": "\n      display: flex;\n      flex-direction: column;\n      flex: 1;\n      overflow: hidden;\n      .node-top {\n        width: 100%;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\n      .node-bottom {\n        color: #adb5bd;\n        width: 100%;\n        overflow: hidden;\n        text-overflow: ellipsis;\n        white-space: nowrap;\n      }"}, {"original": "flex: 0 0 75%;\n      height: 22px;\n      border: 1px solid #409EFF;\n      font-size: 1em;\n      margin-left: -1px;\n      &.error", "type": "class", "names": ["error"], "fullRule": "\n      flex: 0 0 75%;\n      height: 22px;\n      border: 1px solid #409EFF;\n      font-size: 1em;\n      margin-left: -1px;\n      &.error {\n        border: 2px solid #f56c6c;\n      }"}, {"original": ".more", "type": "class", "names": ["more"], "fullRule": "\n    .more {\n      display: none;\n      flex: 0 0 auto;\n      margin-left: auto;\n      padding: 5px 10px;\n    }"}, {"original": "&.active-node", "type": "class", "names": ["active-node"], "fullRule": "\n    &.active-node {\n      background-color: #a6d2ff;\n    }"}, {"original": "&.select-node", "type": "class", "names": ["select-node"], "fullRule": "\n    &.select-node {\n      background-color: #a6d2ff;\n    }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\n      .folder-icon {\n        color: #dee2e6 !important;\n      }"}], "totalSelectors": 14}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\link\\dialog\\edit.vue", "unusedSelectors": [{"original": "height: 28px;\r\n    white-space: nowrap;\r\n    overflow-y: auto;\r\n    user-select: auto;\r\n\r\n    &::-webkit-scrollbar", "type": "tag", "names": ["height"], "fullRule": "\r\n    height: 28px;\r\n    white-space: nowrap;\r\n    overflow-y: auto;\r\n    user-select: auto;\r\n\r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": ".link-icon", "type": "class", "names": ["link-icon"], "fullRule": "\r\n\r\n  .link-icon {\r\n    width: 120px;\r\n    height: 120px;\r\n  }"}, {"original": ":deep(.el-tree-node__content)", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n  :deep(.el-tree-node__content) {\r\n    height: 30px;\r\n  }"}, {"original": ".more", "type": "class", "names": ["more"], "fullRule": "\r\n      .more {\r\n        display: block;\r\n      }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n    .folder-icon {\r\n      color: #ffc107;\r\n      flex: 0 0 auto;\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-right: 5px;\r\n    }"}, {"original": "display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n      .node-top", "type": "class", "names": ["node-top"], "fullRule": "\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n      .node-top {\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\r\n      .node-bottom {\r\n        color: #adb5bd;\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": "flex: 0 0 75%;\r\n      height: 22px;\r\n      border: 1px solid #409EFF;\r\n      font-size: 1em;\r\n      margin-left: -1px;\r\n      &.error", "type": "class", "names": ["error"], "fullRule": "\r\n      flex: 0 0 75%;\r\n      height: 22px;\r\n      border: 1px solid #409EFF;\r\n      font-size: 1em;\r\n      margin-left: -1px;\r\n      &.error {\r\n        border: 2px solid #f56c6c;\r\n      }"}, {"original": ".more", "type": "class", "names": ["more"], "fullRule": "\r\n    .more {\r\n      display: none;\r\n      flex: 0 0 auto;\r\n      margin-left: auto;\r\n      padding: 5px 10px;\r\n    }"}, {"original": "&.active-node", "type": "class", "names": ["active-node"], "fullRule": "\r\n    &.active-node {\r\n      background-color: #a6d2ff;\r\n    }"}, {"original": "&.select-node", "type": "class", "names": ["select-node"], "fullRule": "\r\n    &.select-node {\r\n      background-color: #a6d2ff;\r\n    }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n      .folder-icon {\r\n        color: #dee2e6 !important;\r\n      }"}], "totalSelectors": 14}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\link\\link.vue", "unusedSelectors": [{"original": "// 内容区域\r\n  .content-area", "type": "class", "names": ["content-area"], "fullRule": "\r\n\r\n  // 内容区域\r\n  .content-area {\r\n    flex: 1;\r\n    overflow-y: auto;\r\n  }"}], "totalSelectors": 5}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\package\\package.vue", "unusedSelectors": [{"original": "display: flex;\r\n    height: calc(100vh - 100px);\r\n    padding: 20px;\r\n    overflow-y: auto;\r\n    .drag-wrap", "type": "class", "names": ["drag-wrap"], "fullRule": "\r\n    display: flex;\r\n    height: calc(100vh - 100px);\r\n    padding: 20px;\r\n    overflow-y: auto;\r\n    .drag-wrap {\r\n        border-right: 1px solid var(--gray-500);\r\n    }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\recycler\\components\\doc-detail.vue", "unusedSelectors": [{"original": "cursor: pointer;\r\n\r\n      &:hover", "type": "tag", "names": ["cursor"], "fullRule": "\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        color: #f7f7fa;\r\n      }"}, {"original": "display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: absolute;\r\n    right: 5px;\r\n    top: 5px;\r\n    font-size: 18px;\r\n    width: 22px;\r\n    height: 22px;\r\n    color: #f56c6c;\r\n    cursor: pointer;\r\n    border-radius: 50%;\r\n    &:hover", "type": "id", "names": ["f56c6c"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    position: absolute;\r\n    right: 5px;\r\n    top: 5px;\r\n    font-size: 18px;\r\n    width: 22px;\r\n    height: 22px;\r\n    color: #f56c6c;\r\n    cursor: pointer;\r\n    border-radius: 50%;\r\n    &:hover {\r\n      background: #dee2e6;\r\n    }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\recycler\\recycler.vue", "unusedSelectors": [{"original": ".desc", "type": "class", "names": ["desc"], "fullRule": "\r\n\r\n    .desc {\r\n      color: #888;\r\n      font-size: 14px;\r\n      margin-left: 12px;\r\n    }"}, {"original": "min-height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .el-button--text", "type": "class", "names": ["el-button--text"], "fullRule": "\r\n      min-height: 40px;\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .el-button--text {\r\n        padding-top: 5px;\r\n        padding-bottom: 5px;\r\n      }"}, {"original": "display: flex;\r\n          align-items: center;\r\n          height: 30px;\r\n          &:hover", "type": "tag", "names": ["display"], "fullRule": "\r\n          display: flex;\r\n          align-items: center;\r\n          height: 30px;\r\n          &:hover {\r\n            background: var(--gray-200);\r\n          }"}], "totalSelectors": 6}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\variable\\variable.vue", "unusedSelectors": [{"original": "flex: 0 0 500px;\r\n    margin-right: 10px;\r\n    .file-notice", "type": "class", "names": ["file-notice"], "fullRule": "\r\n    flex: 0 0 500px;\r\n    margin-right: 10px;\r\n    .file-notice {\r\n      white-space: pre-line;\r\n      line-height: 1.2;\r\n    }"}, {"original": ".right", "type": "class", "names": ["right"], "fullRule": "\r\n\r\n  .right {\r\n    flex: 1;\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\websocket\\operation\\operation.vue", "unusedSelectors": [{"original": "flex: 1;\r\n\r\n      :deep(.el-input__inner)", "type": "class", "names": ["el-input__inner"], "fullRule": "\r\n      flex: 1;\r\n\r\n      :deep(.el-input__inner) {\r\n        font-size: 13px;\r\n      }"}, {"original": ".action-buttons", "type": "class", "names": ["action-buttons"], "fullRule": "\r\n\r\n    .action-buttons {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n    }"}, {"original": "height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular", "type": "id", "names": ["d1d5da", "f0f0f0"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n    \r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "<PERSON><PERSON>", "type": "tag", "names": ["<PERSON><PERSON>"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n    \r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Monaco", "type": "tag", "names": ["Monaco"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n    \r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Consolas", "type": "tag", "names": ["Consolas"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n    \r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Liberation Mono", "type": "tag", "names": ["Liberation"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n    \r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "Courier New", "type": "tag", "names": ["Courier"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n    \r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": "monospace;\r\n    \r\n    &::-webkit-scrollbar", "type": "tag", "names": ["monospace"], "fullRule": "\r\n    height: 30px;\r\n    width: 100%;\r\n    display: flex;\r\n    margin: 0;\r\n    align-items: center;\r\n    overflow: hidden;\r\n    padding: 0 10px;\r\n    border: 1px solid #d1d5da;\r\n    border-radius: 4px;\r\n    background-color: #f0f0f0;\r\n    color: #212529;\r\n    font-size: 12px;\r\n    font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n    \r\n    &::-webkit-scrollbar {\r\n      height: 0px;\r\n    }"}, {"original": ".status-tag", "type": "class", "names": ["status-tag"], "fullRule": "\r\n    \r\n    .status-tag {\r\n      flex: 0 0 auto;\r\n      margin-left: auto; \r\n    }"}], "totalSelectors": 12}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\websocket\\params\\after-script\\after-script.vue", "unusedSelectors": [{"original": "flex: 1;\r\n    margin-bottom: 16px;\r\n\r\n    :deep(.el-textarea__inner)", "type": "class", "names": ["el-textarea__inner"], "fullRule": "\r\n    flex: 1;\r\n    margin-bottom: 16px;\r\n\r\n    :deep(.el-textarea__inner) {\r\n      font-family: 'Courier New', monospace;\r\n      font-size: 14px;\r\n    }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\websocket\\params\\config\\config.vue", "unusedSelectors": [{"original": ":deep(.el-form-item__label)", "type": "class", "names": ["el-form-item__label"], "fullRule": "\r\n\r\n  :deep(.el-form-item__label) {\r\n    font-weight: 500;\r\n    color: var(--gray-700);\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\websocket\\params\\headers\\headers.vue", "unusedSelectors": [{"original": ".value-wrap", "type": "class", "names": ["value-wrap"], "fullRule": "\r\n  .value-wrap {\r\n    max-height: 140px;\r\n    overflow-y: auto;\r\n  }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n  .folder-icon {\r\n    color: #ffc107;\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\websocket\\params\\message\\message.vue", "unusedSelectors": [{"original": ".action-options", "type": "class", "names": ["action-options"], "fullRule": "\r\n\r\n      .action-options {\r\n        display: flex;\r\n        align-items: center;\r\n      }"}, {"original": "display: flex;\r\n          align-items: center;\r\n          margin-left: 24px;\r\n\r\n          .interval-unit", "type": "class", "names": ["interval-unit"], "fullRule": "\r\n          display: flex;\r\n          align-items: center;\r\n          margin-left: 24px;\r\n\r\n          .interval-unit {\r\n            font-size: 12px;\r\n            color: var(--el-text-color-regular);\r\n          }"}, {"original": "margin-bottom: 16px;\r\n\r\n    .config-label", "type": "class", "names": ["config-label"], "fullRule": "\r\n    margin-bottom: 16px;\r\n\r\n    .config-label {\r\n      display: block;\r\n      margin-bottom: 8px;\r\n      font-size: 14px;\r\n      font-weight: 500;\r\n      color: var(--el-text-color-primary);\r\n    }"}, {"original": "display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n\r\n      .interval-unit", "type": "class", "names": ["interval-unit"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 8px;\r\n\r\n      .interval-unit {\r\n        font-size: 12px;\r\n        color: var(--el-text-color-regular);\r\n      }"}, {"original": ".heartbeat-content-input", "type": "class", "names": ["heartbeat-content-input"], "fullRule": "\r\n\r\n    .heartbeat-content-input {\r\n      width: 100%;\r\n    }"}, {"original": ".config-actions", "type": "class", "names": ["config-actions"], "fullRule": "\r\n\r\n  .config-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    margin-top: 16px;\r\n    padding-top: 16px;\r\n    border-top: 1px solid var(--el-border-color-lighter);\r\n  }"}], "totalSelectors": 9}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\websocket\\params\\params.vue", "unusedSelectors": [{"original": ".workbench", "type": "class", "names": ["workbench"], "fullRule": "\r\n  padding: 0 0 10px;\r\n  height: calc(100vh - var(--apiflow-apidoc-operation-height) - var(--apiflow-doc-nav-height));\r\n  overflow-y: auto;\r\n  position: relative;\r\n\r\n  .params-tabs,\r\n  .workbench {\r\n    padding-right: 20px;\r\n    padding-left: 20px;\r\n  }"}], "totalSelectors": 2}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\content\\websocket\\params\\pre-script\\pre-script.vue", "unusedSelectors": [{"original": ":deep(.el-alert__content)", "type": "class", "names": ["el-alert__content"], "fullRule": "\r\n    :deep(.el-alert__content) {\r\n      font-size: 13px;\r\n      line-height: 1.4;\r\n    }"}, {"original": "flex: 1;\r\n    \r\n    :deep(.el-textarea__inner)", "type": "class", "names": ["el-textarea__inner"], "fullRule": "\r\n    flex: 1;\r\n    \r\n    :deep(.el-textarea__inner) {\r\n      font-family: '<PERSON>sol<PERSON>', 'Monaco', 'Courier New', monospace !important;\r\n      font-size: 13px;\r\n      line-height: 1.4;\r\n    }"}, {"original": ".script-actions", "type": "class", "names": ["script-actions"], "fullRule": "\r\n\r\n  .script-actions {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n    gap: 8px;\r\n    padding-top: 12px;\r\n    border-top: 1px solid var(--gray-300);\r\n  }"}], "totalSelectors": 5}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\dialog\\save-doc\\save-doc.vue", "unusedSelectors": [{"original": ".more", "type": "class", "names": ["more"], "fullRule": "\r\n      .more {\r\n        display: block;\r\n      }"}, {"original": ".file-icon", "type": "class", "names": ["file-icon"], "fullRule": "\r\n\r\n    .file-icon {\r\n      font-size: 14px;\r\n      margin-right: 5px;\r\n    }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n    .folder-icon {\r\n      color: var(--yellow);\r\n      flex: 0 0 auto;\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-right: 5px;\r\n    }"}, {"original": "display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top", "type": "class", "names": ["node-top"], "fullRule": "\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top {\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\r\n\r\n      .node-bottom {\r\n        color: var(--gray-500);\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": ":deep(.el-tree-node__content)", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n\r\n  :deep(.el-tree-node__content) {\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n  }"}, {"original": ":deep(.el-tree-node__content > .el-tree-node__expand-icon)", "type": "class", "names": ["el-tree-node__content", "el-tree-node__expand-icon"], "fullRule": "\r\n\r\n  :deep(.el-tree-node__content > .el-tree-node__expand-icon) {\r\n    transition: none; //去除所有动画\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n    margin-top: -1px;\r\n  }"}], "totalSelectors": 7}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-edit\\nav\\nav.vue", "unusedSelectors": [{"original": "flex: 0 0 auto;\r\n      height: 40px;\r\n      width: 25px;\r\n      z-index: var(--zIndex-tabs);\r\n      background: var(--gray-200);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      box-shadow: var(--box-shadow-base);\r\n\r\n      &:hover", "type": "tag", "names": ["flex"], "fullRule": "\r\n      flex: 0 0 auto;\r\n      height: 40px;\r\n      width: 25px;\r\n      z-index: var(--zIndex-tabs);\r\n      background: var(--gray-200);\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      cursor: pointer;\r\n      box-shadow: var(--box-shadow-base);\r\n\r\n      &:hover {\r\n        background-color: var(--gray-300);\r\n      }"}, {"original": "display: inline-block;\r\n        width: 130px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n\r\n        // font-size: fz(12);\r\n        &.unfixed", "type": "class", "names": ["unfixed"], "fullRule": "\r\n        display: inline-block;\r\n        width: 130px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n\r\n        // font-size: fz(12);\r\n        &.unfixed {\r\n          // font-family: Verdana, sans-serif;\r\n          // font-style: italic;\r\n          transform: skewX(-10deg);\r\n        }"}, {"original": ".iconfont", "type": "class", "names": ["iconfont"], "fullRule": "\r\n\r\n      .iconfont {\r\n        font-size: 16px;\r\n        display: flex;\r\n        align-items: center;\r\n      }"}, {"original": "position: absolute;\r\n      right: 0;\r\n      width: 25px;\r\n      height: 100%;\r\n      cursor: pointer;\r\n\r\n      &:hover>.has-change", "type": "class", "names": ["has-change"], "fullRule": "\r\n      position: absolute;\r\n      right: 0;\r\n      width: 25px;\r\n      height: 100%;\r\n      cursor: pointer;\r\n\r\n      &:hover>.has-change {\r\n        display: none;\r\n      }"}, {"original": "&:hover>.close", "type": "class", "names": ["close"], "fullRule": "\r\n\r\n      &:hover>.close {\r\n        display: inline-flex !important;\r\n      }"}, {"original": "position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        transform: translate(-50%", "type": "tag", "names": ["position"], "fullRule": "\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        transform: translate(-50%, -50%);\r\n        cursor: pointer;\r\n        line-height: 1.5;\r\n        width: 20px;\r\n        height: 20px;\r\n        border-radius: 50%;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 1;\r\n        font-size: 16px;\r\n\r\n        &:hover {\r\n          background: #ccc;\r\n        }"}, {"original": "position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        transform: translate(-50%", "type": "tag", "names": ["position"], "fullRule": "\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        transform: translate(-50%, -50%);\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 2;\r\n\r\n        .dot {\r\n          width: 10px;\r\n          height: 10px;\r\n          border-radius: 50%;\r\n          background: #36cea1;\r\n        }"}, {"original": "-50%);\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 2;\r\n\r\n        .dot", "type": "class", "names": ["dot"], "fullRule": "\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        transform: translate(-50%, -50%);\r\n        width: 20px;\r\n        height: 20px;\r\n        cursor: pointer;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 2;\r\n\r\n        .dot {\r\n          width: 10px;\r\n          height: 10px;\r\n          border-radius: 50%;\r\n          background: #36cea1;\r\n        }"}, {"original": "//滚动条样式\r\n  .el-scrollbar__bar", "type": "class", "names": ["el-scrollbar__bar"], "fullRule": "\r\n\r\n  //滚动条样式\r\n  .el-scrollbar__bar {\r\n    bottom: 0;\r\n  }"}], "totalSelectors": 10}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-list\\doc-list.vue", "unusedSelectors": [{"original": "width: 70%;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  @media only screen and (max-width: 720px)", "type": "tag", "names": ["width"], "fullRule": "\r\n  width: 70%;\r\n  margin-left: auto;\r\n  margin-right: auto;\r\n  @media only screen and (max-width: 720px) {\r\n    width: 100%;\r\n    padding: 0 20px;\r\n  }"}], "totalSelectors": 1}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-list\\tab-a\\tab-a.vue", "unusedSelectors": [{"original": "display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    @media only screen and (max-width: 720px)", "type": "tag", "names": ["display"], "fullRule": "\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n    align-items: center;\r\n    margin-bottom: 20px;\r\n\r\n    @media only screen and (max-width: 720px) {\r\n      justify-content: center;\r\n    }"}, {"original": ".advance-icon", "type": "class", "names": ["advance-icon"], "fullRule": "\r\n\r\n  .advance-icon {\r\n    cursor: pointer;\r\n  }"}, {"original": "width: 300px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: var(--box-shadow-sm);\r\n    margin-right: 30px;\r\n    margin-bottom: 20px;\r\n    padding: 10px;\r\n    position: relative;\r\n\r\n    @media only screen and (max-width: 720px)", "type": "tag", "names": ["width"], "fullRule": "\r\n    width: 300px;\r\n    border: 1px solid var(--gray-200);\r\n    box-shadow: var(--box-shadow-sm);\r\n    margin-right: 30px;\r\n    margin-bottom: 20px;\r\n    padding: 10px;\r\n    position: relative;\r\n\r\n    @media only screen and (max-width: 720px) {\r\n      margin-right: 0;\r\n      width: 100%;\r\n    }"}, {"original": "width: 25px;\r\n          height: 25px;\r\n          margin-right: 5px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &:hover", "type": "tag", "names": ["width"], "fullRule": "\r\n          width: 25px;\r\n          height: 25px;\r\n          margin-right: 5px;\r\n          cursor: pointer;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n\r\n          &:hover {\r\n            background: var(--gray-200);\r\n          }"}, {"original": ".project-bottom", "type": "class", "names": ["project-bottom"], "fullRule": "\r\n\r\n    .project-bottom {\r\n      width: 100%;\r\n      padding: 10px 0;\r\n      bottom: 10px;\r\n      display: flex;\r\n      align-items: center;\r\n    }"}, {"original": "position: absolute;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 30px;\r\n      height: 30px;\r\n      bottom: 10px;\r\n      right: 10px;\r\n      cursor: pointer;\r\n\r\n      &:hover", "type": "tag", "names": ["position"], "fullRule": "\r\n      position: absolute;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      width: 30px;\r\n      height: 30px;\r\n      bottom: 10px;\r\n      right: 10px;\r\n      cursor: pointer;\r\n\r\n      &:hover {\r\n        background: var(--gray-200);\r\n      }"}, {"original": "i", "type": "tag", "names": ["i"], "fullRule": "\r\n\r\n      i {\r\n        font-size: 18px;\r\n      }"}], "totalSelectors": 8}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-list\\tab-b\\tab-b.vue", "unusedSelectors": [{"original": "position: absolute;\r\n      right: 0px;\r\n      top: 10px;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n      color: var(--gray-600);\r\n      display: none;\r\n      &:hover", "type": "tag", "names": ["position"], "fullRule": "\r\n      position: absolute;\r\n      right: 0px;\r\n      top: 10px;\r\n      font-size: 14px;\r\n      cursor: pointer;\r\n      color: var(--gray-600);\r\n      display: none;\r\n      &:hover {\r\n        color: var(--gray-900);\r\n      }"}, {"original": "margin-left: auto;\r\n      cursor: pointer;\r\n      width: 20px;\r\n      height: 20px;\r\n      border-radius: 50%;\r\n      &:hover", "type": "tag", "names": ["margin"], "fullRule": "\r\n      margin-left: auto;\r\n      cursor: pointer;\r\n      width: 20px;\r\n      height: 20px;\r\n      border-radius: 50%;\r\n      &:hover {\r\n        background-color: var(--gray-200);\r\n      }"}, {"original": "height: 35px;\r\n    line-height: 35px;\r\n    &:hover", "type": "tag", "names": ["height"], "fullRule": "\r\n    height: 35px;\r\n    line-height: 35px;\r\n    &:hover {\r\n      background-color: var(--gray-200);\r\n    }"}, {"original": "&.is-active", "type": "class", "names": ["is-active"], "fullRule": "\r\n    &.is-active {\r\n      background-color: #a6d2ff;\r\n      color: var(--gray-800);\r\n    }"}, {"original": "display: flex;\r\n    align-items: center;\r\n    margin-left: 20px;\r\n    width: 35px;\r\n    height: 35px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: var(--gray-400);\r\n    margin-right: 15px;\r\n    border-radius: 50%;\r\n    border: 1px dashed var(--gray-500);\r\n    cursor: pointer;\r\n    &:hover", "type": "tag", "names": ["display"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    margin-left: 20px;\r\n    width: 35px;\r\n    height: 35px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    color: var(--gray-400);\r\n    margin-right: 15px;\r\n    border-radius: 50%;\r\n    border: 1px dashed var(--gray-500);\r\n    cursor: pointer;\r\n    &:hover {\r\n      background-color: var(--gray-100);\r\n    }"}, {"original": ".user-list", "type": "class", "names": ["user-list"], "fullRule": "\r\n.user-list {\r\n  min-height: 35px;\r\n  max-height: 200px;\r\n}"}], "totalSelectors": 12}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-share\\banner\\banner.vue", "unusedSelectors": [{"original": ".el-input__wrapper", "type": "class", "names": ["el-input__wrapper"], "fullRule": "\r\n      .el-input__wrapper {\r\n        border-radius: 20px;\r\n      }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n    .folder-icon {\r\n      color: var(--yellow);\r\n      flex: 0 0 auto;\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-right: 5px;\r\n    }"}, {"original": "display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top", "type": "class", "names": ["node-top"], "fullRule": "\r\n      display: flex;\r\n      flex-direction: column;\r\n      flex: 1;\r\n      overflow: hidden;\r\n\r\n      .node-top {\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": ".node-bottom", "type": "class", "names": ["node-bottom"], "fullRule": "\r\n\r\n      .node-bottom {\r\n        color: var(--gray-500);\r\n        width: 100%;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n      }"}, {"original": "&.select-node", "type": "class", "names": ["select-node"], "fullRule": "\r\n\r\n    &.select-node {\r\n      background-color: #a6d2ff;\r\n    }"}, {"original": ".folder-icon", "type": "class", "names": ["folder-icon"], "fullRule": "\r\n\r\n      .folder-icon {\r\n        color: var(--gray-500) !important;\r\n      }"}, {"original": "// 禁用动画提高性能\r\n  .el-collapse-transition-enter-active", "type": "class", "names": ["el-collapse-transition-enter-active"], "fullRule": "\r\n\r\n  // 禁用动画提高性能\r\n  .el-collapse-transition-enter-active,\r\n  .el-collapse-transition-leave-active {\r\n    transition: none !important;\r\n  }"}, {"original": ".el-collapse-transition-leave-active", "type": "class", "names": ["el-collapse-transition-leave-active"], "fullRule": "\r\n\r\n  // 禁用动画提高性能\r\n  .el-collapse-transition-enter-active,\r\n  .el-collapse-transition-leave-active {\r\n    transition: none !important;\r\n  }"}, {"original": "align-items: flex-start;\r\n\r\n      &>.el-tree-node__expand-icon", "type": "class", "names": ["el-tree-node__expand-icon"], "fullRule": "\r\n      align-items: flex-start;\r\n\r\n      &>.el-tree-node__expand-icon {\r\n        padding-top: 4px;\r\n      }"}, {"original": ".el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n\r\n  .el-tree-node__content {\r\n    height: auto;\r\n    display: flex;\r\n    align-items: center;\r\n  }"}, {"original": ".el-tree-node__content>.el-tree-node__expand-icon", "type": "class", "names": ["el-tree-node__content", "el-tree-node__expand-icon"], "fullRule": "\r\n\r\n  .el-tree-node__content>.el-tree-node__expand-icon {\r\n    transition: none; //去除所有动画\r\n    padding-top: 0;\r\n    padding-bottom: 0;\r\n    margin-top: -1px;\r\n  }"}], "totalSelectors": 17}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-share\\content\\content.vue", "unusedSelectors": [{"original": "display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  background: var(--gray-100);\r\n  \r\n  h2", "type": "tag", "names": ["display"], "fullRule": "\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  height: 200px;\r\n  background: var(--gray-100);\r\n  \r\n  h2 {\r\n    color: var(--gray-600);\r\n    font-size: 18px;\r\n    font-weight: 500;\r\n    margin: 0;\r\n  }"}, {"original": ".content-format-label", "type": "class", "names": ["content-format-label"], "fullRule": "\r\n    \r\n    .content-format-label {\r\n      font-size: 13px;\r\n      background: #e5d6f6;\r\n      color: var(--purple);\r\n      border-radius: var(--border-radius-sm);\r\n      padding: 2px 8px;\r\n      margin-left: 8px;\r\n      font-weight: normal;\r\n    }"}, {"original": ".api-doc-subtitle", "type": "class", "names": ["api-doc-subtitle"], "fullRule": "\r\n  \r\n  .api-doc-subtitle {\r\n    font-size: 15px;\r\n    color: var(--gray-600);\r\n    margin-bottom: 12px;\r\n    font-weight: 500;\r\n  }"}, {"original": ".api-doc-empty", "type": "class", "names": ["api-doc-empty"], "fullRule": "\r\n  \r\n  .api-doc-empty {\r\n    color: var(--gray-500);\r\n  }"}, {"original": "width: 100%;\r\n    border-collapse: collapse;\r\n    font-size: 14px;\r\n    border: 1px solid var(--gray-300);\r\n    \r\n    th", "type": "tag", "names": ["width"], "fullRule": "\r\n    width: 100%;\r\n    border-collapse: collapse;\r\n    font-size: 14px;\r\n    border: 1px solid var(--gray-300);\r\n    \r\n    th, td {\r\n      padding: 8px 12px;\r\n      text-align: left;\r\n      border: 1px solid var(--gray-300);\r\n    }"}, {"original": "display: flex;\r\n    align-items: center;\r\n    gap: 16px;\r\n    margin-bottom: 8px;\r\n    \r\n    .api-doc-response-title", "type": "class", "names": ["api-doc-response-title"], "fullRule": "\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 16px;\r\n    margin-bottom: 8px;\r\n    \r\n    .api-doc-response-title {\r\n      font-weight: bold;\r\n      color: var(--gray-900);\r\n    }"}, {"original": ".status-code.success", "type": "class", "names": ["status-code", "success"], "fullRule": "\r\n    \r\n    .status-code.success {\r\n      color: var(--success);\r\n      font-weight: bold;\r\n    }"}, {"original": ".type-label", "type": "class", "names": ["type-label"], "fullRule": "\r\n    \r\n    .type-label {\r\n      background: var(--gray-200);\r\n      color: var(--gray-700);\r\n      border-radius: var(--border-radius-sm);\r\n      padding: 0 8px;\r\n      font-size: 15px;\r\n    }"}, {"original": "// 原始数据样式\r\n.api-doc-raw-body", "type": "class", "names": ["api-doc-raw-body"], "fullRule": "\r\n\r\n// 原始数据样式\r\n.api-doc-raw-body {\r\n  background: var(--gray-900);\r\n  color: var(--white);\r\n  border-radius: var(--border-radius-lg);\r\n  padding: 16px;\r\n  overflow-x: auto;\r\n  font-family: SFMono-Regular, Menlo, Monaco, Consolas, Liberation Mono, Courier New, monospace;\r\n  margin-top: 8px;\r\n}"}, {"original": "width: 100%;\r\n  \r\n  :deep(.el-tabs__header)", "type": "class", "names": ["el-tabs__header"], "fullRule": "\r\n  width: 100%;\r\n  \r\n  :deep(.el-tabs__header) {\r\n    margin-bottom: 16px;\r\n  }"}, {"original": "font-size: 14px;\r\n    padding: 8px 16px;\r\n    height: auto;\r\n    line-height: 1.5;\r\n    \r\n    &.is-active", "type": "class", "names": ["is-active"], "fullRule": "\r\n    font-size: 14px;\r\n    padding: 8px 16px;\r\n    height: auto;\r\n    line-height: 1.5;\r\n    \r\n    &.is-active {\r\n      font-weight: bold;\r\n    }"}, {"original": ".api-doc-response-meta", "type": "class", "names": ["api-doc-response-meta"], "fullRule": "\r\n  \r\n  .api-doc-response-meta {\r\n    margin-bottom: 12px;\r\n  }"}], "totalSelectors": 29}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-share\\nav\\nav.vue", "unusedSelectors": [{"original": "display: inline-block;\r\n        width: 130px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n\r\n        &.unfixed", "type": "class", "names": ["unfixed"], "fullRule": "\r\n        display: inline-block;\r\n        width: 130px;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n\r\n        &.unfixed {\r\n          transform: skewX(-10deg);\r\n        }"}, {"original": "position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        transform: translate(-50%", "type": "tag", "names": ["position"], "fullRule": "\r\n        position: absolute;\r\n        left: 50%;\r\n        top: 50%;\r\n        transform: translate(-50%, -50%);\r\n        cursor: pointer;\r\n        line-height: 1.5;\r\n        width: 20px;\r\n        height: 20px;\r\n        border-radius: 50%;\r\n        display: inline-flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        z-index: 1;\r\n        font-size: 16px;\r\n\r\n        &:hover {\r\n          background: #ccc;\r\n        }"}, {"original": "//滚动条样式\r\n  .el-scrollbar__bar", "type": "class", "names": ["el-scrollbar__bar"], "fullRule": "\r\n\r\n  //滚动条样式\r\n  .el-scrollbar__bar {\r\n    bottom: 0;\r\n  }"}], "totalSelectors": 5}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\apidoc\\doc-share\\share.vue", "unusedSelectors": [{"original": "h3", "type": "tag", "names": ["h3"], "fullRule": "\r\n\r\n    h3 {\r\n      margin: 20px 0 10px 0;\r\n      margin: 20px 0 10px 0;\r\n      color: var(--gray-800);\r\n    }"}, {"original": "from", "type": "tag", "names": ["from"], "fullRule": "\r\n  from {\r\n    transform: rotate(0deg);\r\n  }"}, {"original": "to", "type": "tag", "names": ["to"], "fullRule": "\r\n  to {\r\n    transform: rotate(360deg);\r\n  }"}], "totalSelectors": 17}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\permission\\menu\\menu.vue", "unusedSelectors": [{"original": "min-height: 70vh;\r\n\r\n  .el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n  min-height: 70vh;\r\n\r\n  .el-tree-node__content {\r\n    height: 30px;\r\n  }"}, {"original": "width: 100%;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    overflow: hidden;\r\n\r\n    .label", "type": "class", "names": ["label"], "fullRule": "\r\n    width: 100%;\r\n    height: 30px;\r\n    display: flex;\r\n    align-items: center;\r\n    overflow: hidden;\r\n\r\n    .label {\r\n      overflow: hidden;\r\n      text-overflow: ellipsis;\r\n      white-space: nowrap;\r\n    }"}, {"original": ".contextmenu", "type": "class", "names": ["contextmenu"], "fullRule": "\r\n\r\n.contextmenu {\r\n  min-width: 240px;\r\n  position: fixed;\r\n  background: #fff;\r\n  border-radius: 4px;\r\n  box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.1);\r\n  z-index: 1996;\r\n  animation: ctx-fade .2s;\r\n}"}, {"original": "from", "type": "tag", "names": ["from"], "fullRule": "\r\n  from {\r\n    transform: scale(0.8);\r\n    opacity: 0;\r\n  }"}, {"original": "to", "type": "tag", "names": ["to"], "fullRule": "\r\n  to {\r\n    transform: scale(1);\r\n    opacity: 1;\r\n  }"}], "totalSelectors": 5}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\permission\\role\\add\\components\\client-menus.vue", "unusedSelectors": [{"original": "min-height: 200px;\r\n    flex: 0 0 400px;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n    min-height: 200px;\r\n    flex: 0 0 400px;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .el-tree-node__content {\r\n      height: 35px;\r\n    }"}, {"original": ".el-checkbox", "type": "class", "names": ["el-checkbox"], "fullRule": "\r\n\r\n    .el-checkbox {\r\n      margin-bottom: 0;\r\n    }"}, {"original": "display: flex;\r\n      align-items: center;\r\n      height: 30px;\r\n      width: 100%;\r\n\r\n      .node-name", "type": "class", "names": ["node-name"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      height: 30px;\r\n      width: 100%;\r\n\r\n      .node-name {\r\n        display: inline-block;\r\n        max-width: 180px;\r\n      }"}, {"original": ".bg-active", "type": "class", "names": ["bg-active"], "fullRule": "\r\n\r\n      .bg-active {\r\n        background: var(--theme-color);\r\n        color: #fff;\r\n      }"}], "totalSelectors": 19}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\permission\\role\\edit\\components\\client-menus.vue", "unusedSelectors": [{"original": "min-height: 200px;\r\n    flex: 0 0 400px;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .el-tree-node__content", "type": "class", "names": ["el-tree-node__content"], "fullRule": "\r\n    min-height: 200px;\r\n    flex: 0 0 400px;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .el-tree-node__content {\r\n      height: 35px;\r\n    }"}, {"original": ".el-checkbox", "type": "class", "names": ["el-checkbox"], "fullRule": "\r\n\r\n    .el-checkbox {\r\n      margin-bottom: 0;\r\n    }"}, {"original": "display: flex;\r\n      align-items: center;\r\n      height: 30px;\r\n      width: 100%;\r\n\r\n      .node-name", "type": "class", "names": ["node-name"], "fullRule": "\r\n      display: flex;\r\n      align-items: center;\r\n      height: 30px;\r\n      width: 100%;\r\n\r\n      .node-name {\r\n        display: inline-block;\r\n        max-width: 180px;\r\n      }"}, {"original": ".bg-active", "type": "class", "names": ["bg-active"], "fullRule": "\r\n\r\n      .bg-active {\r\n        background: var(--theme-color);\r\n        color: #fff;\r\n      }"}], "totalSelectors": 19}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\cacheManager\\CacheManagement.vue", "unusedSelectors": [{"original": "margin-bottom: 24px;\r\n\r\n    h2", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 24px;\r\n\r\n    h2 {\r\n      margin: 0;\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }"}, {"original": "width: 250px;\r\n      height: 110px;\r\n      background: #fff;\r\n      border-radius: 8px;\r\n      border: 1px solid #eaeaea;\r\n      padding: 16px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n      \r\n      &:hover", "type": "id", "names": ["fff", "eaeaea"], "fullRule": "\r\n      width: 250px;\r\n      height: 110px;\r\n      background: #fff;\r\n      border-radius: 8px;\r\n      border: 1px solid #eaeaea;\r\n      padding: 16px;\r\n      cursor: pointer;\r\n      transition: all 0.3s ease;\r\n      \r\n      &:hover {\r\n        border-color: #c6e2ff;\r\n        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n      }"}, {"original": "display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              padding: 3px;\r\n              &:hover", "type": "tag", "names": ["display"], "fullRule": "\r\n              display: flex;\r\n              align-items: center;\r\n              justify-content: center;\r\n              padding: 3px;\r\n              &:hover {\r\n                background-color: #eee;\r\n              }"}], "totalSelectors": 11}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\cacheManager\\components\\DataBackup.vue", "unusedSelectors": [{"original": "display: inline-block;\r\n    padding: 8px 12px;\r\n    background: var(--bg-light);\r\n    border: 1px solid var(--gray-300);\r\n    border-radius: var(--border-radius);\r\n    \r\n    .info-text", "type": "class", "names": ["info-text"], "fullRule": "\r\n    display: inline-block;\r\n    padding: 8px 12px;\r\n    background: var(--bg-light);\r\n    border: 1px solid var(--gray-300);\r\n    border-radius: var(--border-radius);\r\n    \r\n    .info-text {\r\n      color: var(--gray-600);\r\n      font-size: var(--font-size-xs);\r\n      line-height: 1.4;\r\n    }"}, {"original": ".path-error-message", "type": "class", "names": ["path-error-message"], "fullRule": "\r\n  \r\n  .path-error-message {\r\n    margin-bottom: 16px;\r\n    padding: 8px 12px;\r\n    background: var(--danger-light);\r\n    border: 1px solid var(--danger);\r\n    border-radius: var(--border-radius);\r\n    color: var(--danger);\r\n    font-size: var(--font-size-sm);\r\n  }"}, {"original": ".progress-info", "type": "class", "names": ["progress-info"], "fullRule": "\r\n  .progress-info {\r\n    margin-bottom: 12px;\r\n    color: var(--gray-600);\r\n    font-size: var(--font-size-sm);\r\n  }"}], "totalSelectors": 14}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\cacheManager\\components\\IndexedDBDetail.vue", "unusedSelectors": [{"original": "margin-bottom: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    h3", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }"}, {"original": "margin-top: 24px;\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 5px rgba(0", "type": "id", "names": ["fff"], "fullRule": "\r\n    margin-top: 24px;\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n    padding: 40px 20px;\r\n    text-align: center;\r\n\r\n    .empty-text {\r\n      font-size: 16px;\r\n      color: #999;\r\n    }"}, {"original": "0.1);\r\n    padding: 40px 20px;\r\n    text-align: center;\r\n\r\n    .empty-text", "type": "class", "names": ["empty-text"], "fullRule": "\r\n    margin-top: 24px;\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n    padding: 40px 20px;\r\n    text-align: center;\r\n\r\n    .empty-text {\r\n      font-size: 16px;\r\n      color: #999;\r\n    }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\cacheManager\\components\\LocalStorageDetail.vue", "unusedSelectors": [{"original": "margin-bottom: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    h3", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 16px;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    h3 {\r\n      margin: 0;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }"}, {"original": "margin-top: 24px;\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 5px rgba(0", "type": "id", "names": ["fff"], "fullRule": "\r\n    margin-top: 24px;\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n    padding: 40px 20px;\r\n    text-align: center;\r\n\r\n    .empty-text {\r\n      color: #999;\r\n    }"}, {"original": "0.1);\r\n    padding: 40px 20px;\r\n    text-align: center;\r\n\r\n    .empty-text", "type": "class", "names": ["empty-text"], "fullRule": "\r\n    margin-top: 24px;\r\n    background: #fff;\r\n    border-radius: 8px;\r\n    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);\r\n    padding: 40px 20px;\r\n    text-align: center;\r\n\r\n    .empty-text {\r\n      color: #999;\r\n    }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\cacheManager\\dialog\\indexedDBDialog.vue", "unusedSelectors": [{"original": ".value-content", "type": "class", "names": ["value-content"], "fullRule": "\r\n\r\n\r\n\r\n.value-content {\r\n  position: relative;\r\n  width: 100%;\r\n}"}, {"original": ".value-preview", "type": "class", "names": ["value-preview"], "fullRule": "\r\n\r\n.value-preview {\r\n  font-size: 12px;\r\n  line-height: 1.4;\r\n  color: #495057;\r\n  word-break: break-all;\r\n  white-space: pre-wrap;\r\n  max-height: 60px;\r\n  overflow: hidden;\r\n  padding: 4px 8px;\r\n  background: #f8f9fa;\r\n  border: 1px solid #e9ecef;\r\n  border-radius: 4px;\r\n  transition: all 0.2s ease;\r\n}"}, {"original": ".value-preview.clickable", "type": "class", "names": ["value-preview", "clickable"], "fullRule": "\r\n\r\n.value-preview.clickable {\r\n  cursor: pointer;\r\n  user-select: none;\r\n}"}, {"original": ".value-preview.clickable:hover", "type": "class", "names": ["value-preview", "clickable"], "fullRule": "\r\n\r\n.value-preview.clickable:hover {\r\n  background: #e9ecef;\r\n  border-color: #007bff;\r\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);\r\n}"}, {"original": ".json-popover-content", "type": "class", "names": ["json-popover-content"], "fullRule": "\r\n\r\n.json-popover-content {\r\n  width: 100%;\r\n  max-height: 500px;\r\n  overflow: hidden;\r\n  display: flex;\r\n  flex-direction: column;\r\n}"}, {"original": ".popover-header", "type": "class", "names": ["popover-header"], "fullRule": "\r\n\r\n.popover-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 16px;\r\n  border-bottom: 1px solid #e9ecef;\r\n  background: #f8f9fa;\r\n  border-radius: 8px 8px 0 0;\r\n}"}, {"original": ".popover-title", "type": "class", "names": ["popover-title"], "fullRule": "\r\n\r\n.popover-title {\r\n  font-weight: 500;\r\n  color: #495057;\r\n  font-size: 14px;\r\n}"}, {"original": ".json-editor-container", "type": "class", "names": ["json-editor-container"], "fullRule": "\r\n\r\n.json-editor-container {\r\n  flex: 1;\r\n  overflow: auto;\r\n  padding: 8px;\r\n  background: #fff;\r\n  border-radius: 0 0 8px 8px;\r\n}"}, {"original": ".pagination-container", "type": "class", "names": ["pagination-container"], "fullRule": "\r\n\r\n\r\n.pagination-container {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  padding: 20px 0;\r\n  margin: 0;\r\n}"}, {"original": ".empty-detail", "type": "class", "names": ["empty-detail"], "fullRule": "\r\n\r\n\r\n.empty-detail {\r\n  display: flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  min-height: 200px;\r\n  color: #6c757d;\r\n}"}, {"original": ".empty-text", "type": "class", "names": ["empty-text"], "fullRule": "\r\n\r\n.empty-text {\r\n  font-size: 14px;\r\n  color: #adb5bd;\r\n}"}], "totalSelectors": 18}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\componentLibrary\\ComponentLibrary.vue", "unusedSelectors": [{"original": "width: 100%;\r\n        padding: 10px 15px;\r\n        border: 1px solid #dcdfe6;\r\n        border-radius: 4px;\r\n        font-size: 14px;\r\n        outline: none;\r\n        transition: border-color 0.2s;\r\n        \r\n        &:focus", "type": "id", "names": ["dcdfe6"], "fullRule": "\r\n        width: 100%;\r\n        padding: 10px 15px;\r\n        border: 1px solid #dcdfe6;\r\n        border-radius: 4px;\r\n        font-size: 14px;\r\n        outline: none;\r\n        transition: border-color 0.2s;\r\n        \r\n        &:focus {\r\n          border-color: #007aff;\r\n        }"}, {"original": "border: 1px solid #ebeef5;\r\n        border-radius: 6px;\r\n        padding: 15px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        background-color: #fff;\r\n        transition: all 0.3s;\r\n        \r\n        &:hover", "type": "id", "names": ["ebeef5", "fff"], "fullRule": "\r\n        border: 1px solid #ebeef5;\r\n        border-radius: 6px;\r\n        padding: 15px;\r\n        display: flex;\r\n        flex-direction: column;\r\n        background-color: #fff;\r\n        transition: all 0.3s;\r\n        \r\n        &:hover {\r\n          box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);\r\n          transform: translateY(-2px);\r\n        }"}, {"original": "flex: 1;\r\n          \r\n          h3", "type": "tag", "names": ["flex"], "fullRule": "\r\n          flex: 1;\r\n          \r\n          h3 {\r\n            margin: 0 0 8px 0;\r\n            font-size: 16px;\r\n            font-weight: 500;\r\n          }"}, {"original": "width: 100%;\r\n            padding: 8px 0;\r\n            background-color: #f5f7fa;\r\n            border: 1px solid #dcdfe6;\r\n            border-radius: 4px;\r\n            color: #606266;\r\n            cursor: pointer;\r\n            transition: all 0.3s;\r\n            \r\n            &:hover", "type": "id", "names": ["f5f7fa", "dcdfe6"], "fullRule": "\r\n            width: 100%;\r\n            padding: 8px 0;\r\n            background-color: #f5f7fa;\r\n            border: 1px solid #dcdfe6;\r\n            border-radius: 4px;\r\n            color: #606266;\r\n            cursor: pointer;\r\n            transition: all 0.3s;\r\n            \r\n            &:hover {\r\n              background-color: #ecf5ff;\r\n              color: #007aff;\r\n              border-color: #c6e2ff;\r\n            }"}], "totalSelectors": 9}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\componentLibrary\\components\\Alert.vue", "unusedSelectors": [{"original": "margin-bottom: 20px;\r\n    \r\n    p", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 20px;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      line-height: 1.5;\r\n    }"}], "totalSelectors": 17}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\componentLibrary\\components\\Button.vue", "unusedSelectors": [{"original": "margin-bottom: 20px;\r\n    \r\n    p", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 20px;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      line-height: 1.5;\r\n    }"}, {"original": "background-color: #fff;\r\n        color: #606266;\r\n        border: 1px solid #dcdfe6;\r\n        \r\n        &:hover", "type": "id", "names": ["fff", "dcdfe6"], "fullRule": "\r\n        background-color: #fff;\r\n        color: #606266;\r\n        border: 1px solid #dcdfe6;\r\n        \r\n        &:hover {\r\n          border-color: #007aff;\r\n          color: #007aff;\r\n        }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\componentLibrary\\components\\Dialog.vue", "unusedSelectors": [{"original": "margin-bottom: 20px;\r\n    \r\n    p", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 20px;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      line-height: 1.5;\r\n    }"}, {"original": "padding: 15px 20px;\r\n        border-bottom: 1px solid #ebeef5;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        \r\n        h4", "type": "id", "names": ["ebeef5"], "fullRule": "\r\n        padding: 15px 20px;\r\n        border-bottom: 1px solid #ebeef5;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        \r\n        h4 {\r\n          margin: 0;\r\n          font-size: 16px;\r\n          font-weight: 500;\r\n          color: #333;\r\n        }"}, {"original": "padding: 20px;\r\n        \r\n        p", "type": "tag", "names": ["padding"], "fullRule": "\r\n        padding: 20px;\r\n        \r\n        p {\r\n          margin: 0;\r\n          font-size: 14px;\r\n          color: #606266;\r\n          line-height: 1.5;\r\n        }"}, {"original": "padding: 10px 20px 20px;\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        gap: 10px;\r\n        \r\n        button", "type": "tag", "names": ["padding"], "fullRule": "\r\n        padding: 10px 20px 20px;\r\n        display: flex;\r\n        justify-content: flex-end;\r\n        gap: 10px;\r\n        \r\n        button {\r\n          padding: 8px 16px;\r\n          border-radius: 4px;\r\n          font-size: 14px;\r\n          cursor: pointer;\r\n        }"}, {"original": "background-color: #fff;\r\n          border: 1px solid #dcdfe6;\r\n          color: #606266;\r\n          \r\n          &:hover", "type": "id", "names": ["fff", "dcdfe6"], "fullRule": "\r\n          background-color: #fff;\r\n          border: 1px solid #dcdfe6;\r\n          color: #606266;\r\n          \r\n          &:hover {\r\n            border-color: #c0c4cc;\r\n            color: #333;\r\n          }"}], "totalSelectors": 6}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\componentLibrary\\components\\Dropdown.vue", "unusedSelectors": [{"original": "margin-bottom: 20px;\r\n    \r\n    p", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 20px;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      line-height: 1.5;\r\n    }"}], "totalSelectors": 3}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\componentLibrary\\components\\Input.vue", "unusedSelectors": [{"original": "margin-bottom: 20px;\r\n    \r\n    p", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 20px;\r\n    \r\n    p {\r\n      margin: 0;\r\n      font-size: 14px;\r\n      color: #606266;\r\n      line-height: 1.5;\r\n    }"}, {"original": "display: flex;\r\n      flex-direction: column;\r\n      gap: 8px;\r\n      \r\n      label", "type": "tag", "names": ["display"], "fullRule": "\r\n      display: flex;\r\n      flex-direction: column;\r\n      gap: 8px;\r\n      \r\n      label {\r\n        font-size: 14px;\r\n        color: #606266;\r\n      }"}, {"original": "padding: 8px 12px;\r\n        border: 1px solid #dcdfe6;\r\n        border-radius: 4px;\r\n        font-size: 14px;\r\n        outline: none;\r\n        transition: border-color 0.3s;\r\n        \r\n        &:focus", "type": "id", "names": ["dcdfe6"], "fullRule": "\r\n        padding: 8px 12px;\r\n        border: 1px solid #dcdfe6;\r\n        border-radius: 4px;\r\n        font-size: 14px;\r\n        outline: none;\r\n        transition: border-color 0.3s;\r\n        \r\n        &:focus {\r\n          border-color: #007aff;\r\n        }"}], "totalSelectors": 5}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\UserCenter.vue", "unusedSelectors": [{"original": "// Transition effects\r\n.fade-enter-active", "type": "class", "names": ["fade-enter-active"], "fullRule": "\r\n\r\n// Transition effects\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}"}, {"original": ".fade-leave-active", "type": "class", "names": ["fade-leave-active"], "fullRule": "\r\n\r\n// Transition effects\r\n.fade-enter-active,\r\n.fade-leave-active {\r\n  transition: opacity 0.3s ease;\r\n}"}, {"original": ".fade-enter-from", "type": "class", "names": ["fade-enter-from"], "fullRule": "\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}"}, {"original": ".fade-leave-to", "type": "class", "names": ["fade-leave-to"], "fullRule": "\r\n\r\n.fade-enter-from,\r\n.fade-leave-to {\r\n  opacity: 0;\r\n}"}], "totalSelectors": 9}, {"file": "C:\\Users\\<USER>\\OneDrive\\Desktop\\webs\\apiflow\\packages\\web\\src\\renderer\\pages\\modules\\user-center\\userInfo\\UserInfo.vue", "unusedSelectors": [{"original": "margin-bottom: 24px;\r\n\r\n    h2", "type": "tag", "names": ["margin"], "fullRule": "\r\n    margin-bottom: 24px;\r\n\r\n    h2 {\r\n      margin: 0;\r\n      font-size: 24px;\r\n      font-weight: 600;\r\n      color: #333;\r\n    }"}, {"original": "width: 120px;\r\n        height: 120px;\r\n        // border-radius: 50%;\r\n        overflow: hidden;\r\n        position: relative;\r\n        margin-bottom: 16px;\r\n        // border: 1px solid #ccc;\r\n        img", "type": "id", "names": ["ccc"], "fullRule": "\r\n        width: 120px;\r\n        height: 120px;\r\n        // border-radius: 50%;\r\n        overflow: hidden;\r\n        position: relative;\r\n        margin-bottom: 16px;\r\n        // border: 1px solid #ccc;\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n        }"}, {"original": "position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          right: 0;\r\n          background: rgba(0", "type": "tag", "names": ["position"], "fullRule": "\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          right: 0;\r\n          background: rgba(0, 0, 0, 0.5);\r\n          color: white;\r\n          height: 40px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          opacity: 0;\r\n          transition: opacity 0.3s;\r\n          \r\n          i {\r\n            font-size: 20px;\r\n          }"}, {"original": "padding: 8px 15px;\r\n        background: #ecf5ff;\r\n        color: #409eff;\r\n        border: 1px solid #d9ecff;\r\n        border-radius: 4px;\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        transition: all 0.3s ease;\r\n        \r\n        &:hover", "type": "id", "names": ["ecf5ff", "d9ecff"], "fullRule": "\r\n        padding: 8px 15px;\r\n        background: #ecf5ff;\r\n        color: #409eff;\r\n        border: 1px solid #d9ecff;\r\n        border-radius: 4px;\r\n        cursor: pointer;\r\n        font-size: 14px;\r\n        transition: all 0.3s ease;\r\n        \r\n        &:hover {\r\n          background: #d9ecff;\r\n          color: #3a8ee6;\r\n        }"}, {"original": "width: 300px;\r\n            padding: 8px 12px;\r\n            border: 1px solid #dcdfe6;\r\n            border-radius: 4px;\r\n            color: #606266;\r\n            transition: all 0.3s;\r\n            \r\n            &:focus", "type": "id", "names": ["dcdfe6"], "fullRule": "\r\n            width: 300px;\r\n            padding: 8px 12px;\r\n            border: 1px solid #dcdfe6;\r\n            border-radius: 4px;\r\n            color: #606266;\r\n            transition: all 0.3s;\r\n            \r\n            &:focus {\r\n              border-color: #409eff;\r\n              outline: none;\r\n            }"}, {"original": "background: white;\r\n            border: 1px solid #dcdfe6;\r\n            color: #606266;\r\n            \r\n            &:hover", "type": "id", "names": ["dcdfe6"], "fullRule": "\r\n            background: white;\r\n            border: 1px solid #dcdfe6;\r\n            color: #606266;\r\n            \r\n            &:hover {\r\n              color: #409eff;\r\n              border-color: #c6e2ff;\r\n              background-color: #ecf5ff;\r\n            }"}], "totalSelectors": 8}]