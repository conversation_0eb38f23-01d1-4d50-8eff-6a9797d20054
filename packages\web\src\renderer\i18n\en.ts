export default {
  基于Vue和Electron的接口文档工具: '基于Vue和Electron的接口文档工具',
  项目列表: 'Project List',
  项目名称: 'Project Name',
  GitHub地址: 'GitHub Address',
  Gitee地址: 'Gitee Address',
  最近一次更新: 'Last Update',
  api文档: 'API Documentation',
  权限管理: 'Permission Management',
  刷新: 'Refresh',
  前进: 'Forward',
  后退: 'Back',
  切换语言: 'Switch Language',
  更新进度: 'Update Progress',
  安装: 'Install',
  个人中心: 'Personal Center',
  检查更新: 'Check for Updates',
  版本: 'Version',
  退出登录: 'Logout',
  存在可用更新: 'Available updates exist',
  没有可用更新: 'No available updates',
  暂无可用更新: 'No available updates',
  下载中: 'Downloading',
  下载完成: 'Download Complete',
  更新异常请稍后再试: 'Update error, please try again later',
  账号登录: 'Account Login',
  手机登录: 'Phone Login',
  账号注册: 'Account Registration',
  忘记密码: 'Forgot Password',
  请输入用户名: 'Please enter username',
  请输入密码: 'Please enter password',
  '直接登录(体验账号，数据不会被保存)': 'Direct Login (Trial account, data will not be saved)',
  登录: 'Login',
  注册账号: 'Register Account',
  '已有账号，忘记密码?': 'Have an account, forgot password?',
  跳转github: 'Go to GitHub',
  跳转码云: 'Go to Gitee',
  跳转文档: 'Go to Documentation',
  完整文档: 'Complete Documentation',
  跳转部署文档: 'Go to Deployment Documentation',
  部署文档: 'Deployment Documentation',
  客户端下载: 'Client Download',
  验证码: 'Verification Code',
  请输入手机号: 'Please enter phone number',
  请输入验证码: 'Please enter verification code',
  请填写正确手机号: 'Please enter correct phone number',
  请输入登录名称: 'Please enter login name',
  请再次输入密码: 'Please enter password again',
  注册并登录: 'Register and Login',
  请输入重置后密码: 'Please enter new password',
  '只允许 数字  字符串 ! @ # 不允许其他字符串': 'Only numbers, strings, ! @ # are allowed, no other characters',
  '数字+字符串，并且大于8位': 'Numbers + strings, and more than 8 characters',
  请完善必填信息: 'Please complete required information',
  '两次输入密码不一致!': 'Passwords do not match!',
  重置密码成功: 'Password reset successful',
  确定批量删除: 'Confirm Batch Delete',
  '个节点?': ' nodes?',
  节点: 'Node',
  提示: 'Tip',
  确定: 'Confirm',
  取消: 'Cancel',
  确定删除: 'Confirm Delete',
  新增文件夹: 'New Folder',
  新增文件: 'New File',
  刷新banner: 'Refresh Banner',
  项目分享: 'Online Link',
  回收站: 'Recycle Bin',
  导出文档: 'Export Documentation',
  导入文档: 'Import Documentation',
  操作审计: 'Operation Audit',
  全局设置: 'Global Settings',
  '文档名称、文档url': 'Document Name, Document URL',
  高级筛选: 'Advanced Filter',
  操作人员: 'Operator',
  清空: 'Clear',
  录入日期: 'Entry Date',
  今天: 'Today',
  近两天: 'Last 2 Days',
  近三天: 'Last 3 Days',
  近七天: 'Last 7 Days',
  自定义: 'Custom',
  至: 'To',
  开始日期: 'Start Date',
  结束日期: 'End Date',
  最近多少条: 'Recent Records',
  '2条': '2 Records',
  '5条': '5 Records',
  '10条': '10 Records',
  '15条': '15 Records',
  更多操作: 'More Operations',
  快捷操作: 'Quick Operations',
  点击发起连接建立WebSocket连接: 'Click toolbar button to add document or right-click to add',
  不能为空: 'Cannot be empty',
  新建文档: 'New Document',
  新建文件夹: 'New Folder',
  剪切: 'Cut',
  复制: 'Copy',
  生成副本: 'Generate Copy',
  粘贴: 'Paste',
  重命名: 'Rename',
  删除: 'Delete',
  批量剪切: 'Batch Cut',
  批量复制: 'Batch Copy',
  批量删除: 'Batch Delete',
  以模板新建: 'New from Template',
  单个文件夹里面文档个数不超过: 'Documents in a single folder cannot exceed',
  全局设置中可配置: 'Configurable in global settings',
  个: '',
  '域名、接口前缀、环境维护': 'Domain, API Prefix, Environment Maintenance',
  符合规范的接口前缀: 'Standard API Prefix',
  'ip地址+路径(可选)': 'IP Address + Path (Optional)',
  例如: 'Example',
  '域名+路径(可选)': 'Domain + Path (Optional)',
  前缀名称: 'Prefix Name',
  '例如：张三本地': 'Example: Zhang San Local',
  'ip+端口或域名': 'IP + Port or Domain',
  请选择协议: 'Please select protocol',
  没有则不填: 'Leave blank if none',
  不填则默认80: 'Default 80 if not filled',
  端口: 'Port',
  ip或域名: 'IP or Domain',
  协议: 'Protocol',
  接口前缀: 'API Prefix',
  是否共享: 'Whether to Share',
  仅自身可见: 'Only Self Visible',
  项目内成员可见: 'Visible to Project Members',
  确认添加: 'Confirm Add',
  接口前缀必填: 'API Prefix Required',
  操作: 'Operation',
  编辑: 'Edit',
  确认: 'Confirm',
  关闭: 'Close',
  接口前缀不符合规范: 'API Prefix does not meet standards',
  请输入前缀名称: 'Please enter prefix name',
  限制可维护域名数不超过: 'Limit maintainable domains to no more than',
  修改成功: 'Modified Successfully',
  '此操作将永久删除此条记录, 是否继续?': 'This operation will permanently delete this record, continue?',
  接口前缀不能为空: 'API Prefix cannot be empty',
  '当前请求方法被禁止，可以在全局配置中进行相关配置': 'Current request method is forbidden, can be configured in global settings',
  Mock服务器: 'Mock Server',
  环境维护: 'Environment Maintenance',
  代理: 'Proxy',
  '路径参数写法': 'Path Parameter Syntax',
  '由于浏览器限制，非electron环境无法模拟发送请求': 'Due to browser limitations, non-electron environment cannot simulate sending requests',
  发送请求: 'Send Request',
  取消请求: 'Cancel Request',
  保存接口: 'Save Interface',
  导入参数: 'Import Parameters',
  格式化JSON: 'Format JSON',
  确认导入: 'Confirm Import',
  无法解析该字符串: 'Unable to parse this string',
  保存参数为模板: 'Save Parameters as Template',
  模板名称: 'Template Name',
  保存: 'Save',
  应用模板: 'Apply Template',
  过滤模板: 'Filter Template',
  维护: 'Maintain',
  暂无数据: 'No Data',
  保存为模板: 'Save as Template',
  'raw模块中json数据可用于快速调试，参数无法添加备注，如果需要添加备注可以选择在json模块中录入参数': 'JSON data in raw module can be used for quick debugging, parameters cannot add remarks, if you need to add remarks you can choose to enter parameters in the json module',
  点击隐藏: 'Click to Hide',
  个隐藏: 'hidden',
  原始值: 'Original Value',
  参数: 'Parameter',
  名称: 'Name',
  修改名称: 'Modify Name',
  状态码: 'Status Code',
  返回格式: 'Return Format',
  新增: 'Add',
  请求参数: 'Request Parameters',
  Query参数: 'Query Parameters',
  Path参数: 'Path Parameters',
  'Body参数(application/json)': 'Body Parameters (application/json)',
  'Body参数(multipart/formdata)': 'Body Parameters (multipart/formdata)',
  'Body参数(x-www-form-urlencoded)': 'Body Parameters (x-www-form-urlencoded)',
  Body参数: 'Body Parameters',
  返回参数: 'Return Parameters',
  请求头: 'Request Headers',
  预览: 'Preview',
  布局: 'Layout',
  左右布局: 'Left-Right Layout',
  上下布局: 'Top-Bottom Layout',
  变量: 'Variables',
  联想值: 'Associated Values',
  备注信息: 'Remark Information',
  未实现的返回类型: 'Unimplemented Return Type',
  变量维护: 'Variable Maintenance',
  联想参数: 'Associated Parameters',
  基本信息: 'Basic Information',
  请求地址: 'Request URL',
  请求方式: 'Request Method',
  维护人员: 'Maintainer',
  创建人员: 'Creator',
  累计用时: 'Total Time',
  更新日期: 'Update Date',
  创建日期: 'Create Date',
  下载文件: 'Download File',
  应用为响应值: 'Apply as Response Value',
  应用为: 'Apply as',
  未命名: 'Unnamed',
  总大小: 'Total Size',
  已传输: 'Transferred',
  进度: 'Progress',
  值: 'Value',
  时长: 'Duration',
  未请求数据: 'No Request Data',
  大小: 'Size',
  格式: 'Format',
  返回值: 'Return Value',
  返回头: 'Return Headers',
  点击发送请求按钮发送请求: 'Click send request button to send request',
  '因浏览器限制，完整HTTP功能请下载Electron': 'Due to browser limitations, please download Electron for complete HTTP functionality',
  '跨域、、请求头(user-agent,accept-encoding)等受限': 'Cross-origin, request headers (user-agent, accept-encoding) are restricted',
  下载Electron: 'Download Electron',
  导出类型: 'Export Type',
  JSON文档: 'JSON Documentation',
  导出到其他项目: 'Export to Other Projects',
  额外配置: 'Additional Configuration',
  选择导出: 'Select Export',
  开启后可以自由选择需要导出的文档: 'After enabling, you can freely select documents to export',
  总数: 'Total',
  文件夹数量: 'Folder Count',
  文档数量: 'Document Count',
  确定导出: 'Confirm Export',
  请至少选择一个文档导出: 'Please select at least one document to export',
  将当前项目指定文档导出到其他项目: 'Export specified documents from current project to other projects',
  '从左侧拖拽文档到右侧，右侧也可以进行简单的拖拽': 'Drag documents from left to right, right side can also perform simple drag operations',
  鼠标右键可以新增文件夹或者删除文件夹: 'Right-click to add or delete folders',
  '暂无文档，请在项目中添加至少一个文档': 'No documents, please add at least one document to the project',
  导入成功: 'Import Successful',
  当前版本: 'Current Version',
  今日新增: 'Today\'s New',
  接口总数: 'Total Interfaces',
  '支持：摸鱼文档、Swagger/OpenApi 3.0/Postman2.1': 'Support: Mock API Doc, Swagger/OpenApi 3.0/Postman2.1',
  '将文件拖到此处，或': 'Drag files here, or',
  点击上传: 'Click to Upload',
  文档类型: 'Document Type',
  导入数据预览: 'Import Data Preview',
  文档数: 'Document Count',
  文件夹数: 'Folder Count',
  文件夹命名方式: 'Folder Naming Method',
  'none代表不存在文件夹，所有节点扁平放置': 'none means no folders, all nodes are placed flat',
  导入方式: 'Import Method',
  请谨慎选择导入方式: 'Please carefully choose import method',
  追加方式: 'Append Method',
  覆盖方式: 'Overwrite Method',
  目标目录: 'Target Directory',
  '选择需要挂载的节点，不选择则默认挂载到根目录': 'Select nodes to mount, if not selected, default to root directory',
  确定导入: 'Confirm Import',
  '未知的文件格式，无法解析': 'Unknown file format, cannot parse',
  仅支持JSON格式或者YAML格式文件: 'Only supports JSON or YAML format files',
  文件大小不超过: 'File size cannot exceed',
  覆盖后的数据将无法还原: 'Overwritten data cannot be restored',
  请选择需要导入的文件: 'Please select file to import',
  缺少Version信息: 'Missing Version information',
  缺少Info字段: 'Missing Info field',
  缺少servers字段: 'Missing servers field',
  servers字段必须为数组: 'servers field must be an array',
  'server对象中存在多个变量枚举值，但接口工具仅解析默认值': 'Multiple variable enum values exist in server object, but interface tool only parses default values',
  服务器: 'Server',
  缺少paths字段: 'Missing paths field',
  路径: 'Path',
  相关属性为空: 'Related properties are empty',
  paths参数中存在方法: 'Method exists in paths parameters',
  但是所匹配数据为空: 'But matched data is empty',
  链接名称: 'Link Name',
  生成链接: 'Generate Link',
  过期截至: 'Expiration Date',
  链接: 'Link',
  密码: 'Password',
  不需要密码: 'No Password Required',
  字段名: 'Field Name',
  参数字段名称: 'Parameter Field Name',
  类型: 'Type',
  Path参数个数: 'Path Parameter Count',
  Query参数个数: 'Query Parameter Count',
  Body参数个数: 'Body Parameter Count',
  Response参数个数: 'Response Parameter Count',
  参数名称: 'Parameter Name',
  备注: 'Remark',
  参数值: 'Parameter Value',
  参数类型: 'Parameter Type',
  是否删除当前参数: 'Delete current parameter?',
  确定批量删除当前选中节点: 'Confirm batch delete currently selected nodes',
  过滤条件: 'Filter Conditions',
  新增模板: 'Add Template',
  '例如：默认返回值': 'Example: Default Return Value',
  '请求参数(Params)': 'Request Parameters (Params)',
  '请求参数(Body)': 'Request Parameters (Body)',
  确认新增: 'Confirm Add',
  修改模板: 'Modify Template',
  请选择参数类型: 'Please select parameter type',
  创建者名称: 'Creator Name',
  参数模板: 'Parameter Template',
  请输入模板名称: 'Please enter template name',
  请选择模板类型: 'Please select template type',
  新增变量: 'Add Variable',
  变量名称: 'Variable Name',
  请输入变量名称: 'Please enter variable name',
  变量值: 'Variable Value',
  请输入变量值: 'Please enter variable value',
  值类型: 'Value Type',
  变量列表: 'Variable List',
  创建者: 'Creator',
  '此操作将永久删除该域名, 是否继续?': 'This operation will permanently delete this domain, continue?',
  '此操作将永久删除该变量, 是否继续?': 'This operation will permanently delete this variable, continue?',
  删除成功: 'Delete Successful',
  新增文档: 'Add Document',
  文档名称: 'Document Name',
  文件夹名称: 'Folder Name',
  关闭右侧: 'Close Right',
  关闭左侧: 'Close Left',
  关闭其他: 'Close Others',
  全部关闭: 'Close All',
  全部: 'All',
  强制全部关闭: 'Force Close All',
  新增项目: 'Add Project',
  选择成员: 'Select Members',
  请输入项目名称: 'Please enter project name',
  输入用户名或完整手机号查找用户: 'Enter username or complete phone number to find user',
  用户名: 'Username',
  昵称: 'Nickname',
  '角色(权限)': 'Role (Permission)',
  只读: 'Read Only',
  仅查看项目: 'View Project Only',
  读写: 'Read Write',
  新增和编辑文档: 'Add and Edit Documents',
  管理员: 'Administrator',
  添加新成员: 'Add New Member',
  请填写项目名称: 'Please fill in project name',
  请勿重复添加: 'Please do not add duplicates',
  '用户已存在、请勿重复添加': 'User already exists, please do not add duplicates',
  修改项目: 'Modify Project',
  添加用户: 'Add User',
  '确认删除当前成员吗?': 'Confirm delete current member?',
  团队至少保留一个管理员: 'Team must retain at least one administrator',
  '确认离开当前团队吗?': 'Confirm leave current team?',
  '确认改变当前管理员权限吗?': 'Confirm change current administrator permissions?',
  成员管理: 'Member Management',
  新建项目: 'New Project',
  导入项目: 'Import Project',
  收藏的项目: 'Favorited Projects',
  收藏: 'Favorite',
  取消收藏: 'Unfavorite',
  最新更新: 'Latest Update',
  接口数: 'Interface Count',
  全部项目: 'All Projects',
  团队管理: 'Team Management',
  过期倒计时: 'Expiration Countdown',
  确认密码: 'Confirm Password',
  无效的项目id: 'Invalid project ID',
  '当前接口不存在，可能已经被删除!': 'Current interface does not exist, may have been deleted!',
  关闭接口: 'Close Interface',
  发送请求时候自动计算: 'Auto-calculate when sending request',
  消息的长度: 'Message Length',
  发送请求时候自动处理: 'Auto-process when sending request',
  用户代理软件信息: 'User Agent Software Information',
  主机信息: 'Host Information',
  客户端理解的编码方式: 'Client-understood Encoding Method',
  '当前的事务完成后，是否会关闭网络连接': 'Whether to close network connection after current transaction completes',
  根据body类型自动处理: 'Auto-process based on body type',
  返回参数名称: 'Return Parameter Name',
  是否要保存对接口的修改: 'Save modifications to interface?',
  '维护人员：': 'Maintainer: ',
  '创建人员：': 'Creator: ',
  '累计用时：': 'Total Time: ',
  '更新日期：': 'Update Date: ',
  '创建日期：': 'Create Date: ',
  '确定批量删除个节点?': 'Confirm batch delete nodes?',
  '确定删除节点?': 'Confirm delete node?',
  未知请求类型: 'Unknown Request Type',
  模板维护: 'Template Maintenance',
  新增前端路由: 'Add Frontend Route',
  分组名称: 'Group Name',
  '名称&地址': 'Name & Address',
  新增路由: 'Add Route',
  批量修改类型: 'Batch Modify Type',
  路由名称: 'Route Name',
  路由地址: 'Route Address',
  编辑菜单: 'Edit Menu',
  菜单名称: 'Menu Name',
  菜单列表: 'Menu List',
  新增子菜单: 'Add Submenu',
  支持鼠标右键新增和编辑菜单: 'Support right-click to add and edit menus',
  菜单可以进行拖拽排序: 'Menus can be drag-sorted',
  参数值不能为null: 'Parameter value cannot be null',
  全选: 'Select All',
  修改角色: 'Modify Role',
  角色名称: 'Role Name',
  前端路由: 'Frontend Routes',
  后端路由: 'Backend Routes',
  前端菜单: 'Frontend Menus',
  创建时间: 'Create Time',
  新增角色: 'Add Role',
  修改服务端路由: 'Modify Server Route',
  请求方法: 'Request Method',
  批量修改服务端路由类型: 'Batch Modify Server Route Type',
  '新增用户': 'Add User',
  手机号: 'Phone Number',
  角色选择: 'Role Selection',
  修改: 'Modify',
  基础信息: 'Basic Information',
  下载模板: 'Download Template',
  导入用户: 'Import Users',
  上次登录: 'Last Login',
  登录次数: 'Login Count',
  角色信息: 'Role Information',
  状态: 'Status',
  启用: 'Enable',
  禁用: 'Disable',
  确实要该用户吗: 'Confirm this user?',
  角色维护: 'Role Maintenance',
  菜单维护: 'Menu Maintenance',
  '后端路由(接口)': 'Backend Routes (Interfaces)',
  常用: 'Common',
  '日期/时间': 'Date/Time',
  图片: 'Image',
  中文文本: 'Chinese Text',
  英文文本: 'English Text',
  地区相关: 'Region Related',
  颜色: 'Color',
  中文名称: 'Chinese Name',
  中文单词: 'Chinese Word',
  中文句子: 'Chinese Sentence',
  中文段落: 'Chinese Paragraph',
  中文标题: 'Chinese Title',
  英文名称: 'English Name',
  英文句子: 'English Sentence',
  英文单词: 'English Word',
  英文标题: 'English Title',
  布尔值: 'Boolean',
  '自然数(0,1,2,3,4)': 'Natural Numbers (0,1,2,3,4)',
  '自然数(大于100)': 'Natural Numbers (>100)',
  '自然数(大于100小于200)': 'Natural Numbers (>100, <200)',
  '整数(-22,1,23)': 'Integers (-22,1,23)',
  '整数(大于100)': 'Integers (>100)',
  '整数(大于100小于200)': 'Integers (>100, <200)',
  浮点数: 'Float',
  字符串: 'String',
  英文段落: 'English Paragraph',
  数字: 'Number',
  '字符串(长度为5)': 'String (Length 5)',
  '时间戳(精确到毫秒13位)': 'Timestamp (13 digits, millisecond precision)',
  日期时间: 'DateTime',
  '日期(年月日)': 'Date (YYYY-MM-DD)',
  '时间(时分秒)': 'Time (HH:MM:SS)',
  当前日期时间: 'Current DateTime',
  '颜色(#ff6600)': 'Color (#ff6600)',
  '颜色(rgb(122,122,122))': 'Color (rgb(122,122,122))',
  '颜色rgb(122,122,122, 0.3)': 'Color rgb(122,122,122, 0.3)',
  '颜色hsl(222, 11, 31)': 'Color hsl(222, 11, 31)',
  '图片(150x100)': 'Image (150x100)',
  base64图片数据: 'Base64 Image Data',
  base64图片数据100x100: 'Base64 Image Data 100x100',
  省: 'Province',
  市: 'City',
  区: 'District',
  此项不允许删除: 'This item cannot be deleted',
  删除当前行: 'Delete Current Row',
  传输数据类型为formData才能使用file类型: 'File type can only be used when transmission data type is formData',
  对象和数组不必填写参数值: 'Objects and arrays do not need to fill parameter values',
  请选择: 'Please Select',
  选择文件: 'Select File',
  必有: 'Required',
  参数描述与备注: 'Parameter Description and Remarks',
  '参数不允许嵌套，例如：当请求方式为get时，请求参数只能为扁平数据': 'Parameters cannot be nested, e.g., when request method is GET, request parameters can only be flat data',
  添加一条嵌套数据: 'Add a Nested Data',
  根元素: 'Root Element',
  父元素为数组不必填写参数名称: 'Parent element is array, parameter name not required',
  输入参数名称自动换行: 'Auto-wrap when entering parameter name',
  校验: 'Validation',
  '参数类型不允许改变，eg：当请求方式为get时，请求参数类型只能为string': 'Parameter type cannot be changed, e.g., when request method is GET, request parameter type can only be string',
  对象类型不必填写: 'Object type not required to fill',
  填写数字代表mock数据条数: 'Fill number represents mock data count',
  '参数值、@开头代表mock数据': 'Parameter value, @ prefix represents mock data',
  可选: 'Optional',
  请输入标题: 'Please enter title',
  加载中: 'Loading',
  请输入: 'Please enter',
  双击还原: 'Double-click to restore',
  获取验证码: 'Get Verification Code',
  重新发送: 'Resend',
  重新获取: 'Regenerate',
  序号: 'Serial Number',
  在左侧进行数据选择后方可删除数据: 'Select data on the left before deleting',
  '此操作将删除条记录, 是否继续?': 'This operation will delete records, continue?',
  用户: 'User',
  请求url不能为空: 'Request URL cannot be empty',
  参数位置: 'Parameter Position',
  清除所有缓存: 'Clear All Cache',
  前置脚本: 'Pre-request Script',
  变量类型: 'Variable Type',
  后置脚本: 'Post-request Script',
  请求信息: 'Request Information',
  生成代码: 'Generate Code',
  '开始时间，可接受两个可选参数startTime(\'2022-xx-xx\', \'YYYY-MM-DD\')': 'Start time, accepts two optional parameters startTime(\'2022-xx-xx\', \'YYYY-MM-DD\')',
  '结束时间(结束时间晚于开始时间)': 'End time (end time later than start time)',
  点击工具栏按钮新建接口或者鼠标右键新增: 'Click toolbar button to create new interface or right-click to add',
  '参数值、@代表mock，{{ 变量 }}': 'Parameter value, @ represents mock, {{ variable }}',
  新建接口: 'New Interface',
  设置公共请求头: 'Set Common Request Headers',
  前缀值: 'Prefix Value',
  什么是接口前缀: 'What is API Prefix',
  '域名、接口前缀': 'Domain, API Prefix',
  '时间戳(精确到秒10位)': 'Timestamp (10 digits, second precision)',
  接口编排: 'Interface Orchestration',
  返回首页: 'Return to Home',
  退出: 'Exit',
  '文件名称：': 'File Name: ',
  'mime类型：': 'MIME Type: ',
  '文件路径：': 'File Path: ',
  '注意：': 'Note: ',
  '若file类型变量大于10kb则会自动转换为本地附件地址，这可能导致隐私泄露，请仅添加授信成员': 'If file type variable is larger than 10kb, it will automatically convert to local attachment address, which may cause privacy leakage, please only add trusted members',
  密码设置: 'Password Settings',
  密码可不填写: 'Password is optional',
  请输入链接名称: 'Please enter link name',
  '请输入链接名称 eg:xxx团队': 'Please enter link name eg:xxx team',
  过期时间: 'Expiration Time',
  '不填默认一个月后过期，最大日期为一年': 'If not filled, it will expire in one month by default, maximum date is one year',
  '1天后': '1 day later',
  '1周后': '1 week later',
  '1个月后': '1 month later',
  '1个季度后': '1 quarter later',
  不过期: 'Never expire',
  选择分享: 'Select Share',
  '开启后可以自由选择需要分享的文档': 'After enabling, you can freely select documents to share',
  确认修改: 'Confirm Modify',
  '请至少选择一个文档分享': 'Please select at least one document to share',
  天后: ' days later',
  文档分享: 'Document Share',
  分享链接: 'Share Link',
  验证分享链接: 'Verify Share Link',
  正在验证分享链接: 'Verifying Share Link',
  永久有效: 'Permanent',
  文档内容: 'Document Content',
  这里是分享的API文档内容: 'This is the shared API document content',
  密码验证成功: 'Password verification successful',
  密码错误: 'Wrong password',
  密码验证失败: 'Password verification failed',
  请输入访问密码: 'Please enter access password',
  分享链接无效缺少分享ID: 'Invalid share link, missing share ID',
  获取分享信息失败请检查链接是否正确: 'Failed to get share information, please check if the link is correct',
  已过期: 'Expired',
  天: 'd',
  小时: 'h',
  分: 'm',
  秒: 's',
  认证中: 'Authenticating...',
  获取分享信息失败: 'Failed to get share information',
  'shareId为空，无法获取banner数据': 'ShareID is empty, unable to get banner data',
  '获取分享banner数据失败': 'Failed to get share banner data',
  '获取分享数据失败，请检查分享链接是否有效': 'Failed to get share data, please check if the share link is valid',
  更新于: 'Updated at',
  '暂无Query参数': 'No Query Parameters',
  参数名: 'Parameter Name',
  必填: 'Required',
  描述: 'Description',
  是: 'Yes',
  否: 'No',
  暂无请求头: 'No Request Headers',
  暂无请求体参数: 'No Body Parameters',
  响应: 'Response',
  暂无响应数据: 'No Response Data',
  暂无标签页: 'No Tabs',
  分钟: 'Minutes',
  api_import_title: 'API Documentation Import',
  api_import_desc: 'Import and validate your Swagger or OpenAPI 3.1 specifications',
  详情: 'Details',
  键名: 'Key',
  无法解析数据: 'Unable to parse data',
  // WebSocket related translations
  心跳包间隔: 'Heartbeat Interval',
  自动发送心跳包: 'Auto Send Heartbeat',
  自动发送: 'Auto Send',
  发送并清空: 'Send and Clear',
  心跳包内容: 'Heartbeat Content',
  启用自动心跳: 'Enable Auto Heartbeat',
  心跳间隔时间: 'Heartbeat Interval Time',
  毫秒: 'ms',
  自定义心跳内容: 'Custom Heartbeat Content',
  心跳包发送成功: 'Heartbeat sent successfully',
  心跳包发送失败: 'Heartbeat send failed',
  请输入心跳包内容: 'Please enter heartbeat content',
  心跳包间隔不能为空: 'Heartbeat interval cannot be empty',
  心跳包间隔必须大于0: 'Heartbeat interval must be greater than 0',
  自动心跳已启用: 'Auto heartbeat enabled',
  自动心跳已停止: 'Auto heartbeat stopped',
  发送间隔: 'Send Interval',
  
  // New untranslated text
  '本地': 'Local',
  中文简体: 'Simplified Chinese',
  中文繁體: 'Traditional Chinese',
  主页面: 'Home',
  最小化: 'Minimize',
  最大化: 'Maximize',
  取消最大化: 'Unmaximize',
  刷新主应用: 'Refresh Main App',
  切换变量选择模式支持变量或者直接选择文件: 'Switch variable selection mode, supports variables or direct file selection',
  变量模式: 'Variable Mode',
  文件模式: 'File Mode',
  不允许新增数据: 'Adding data not allowed',
  不允许删除数据: 'Deleting data not allowed',
  该请求头无法修改也无法取消发送: 'This request header cannot be modified or cancelled',
  执行中: 'Executing...',
  此操作将清空所有本地缓存是否继续: 'This operation will clear all local cache, continue?',
  已被删除: 'Deleted',
  连接中: 'Connecting',
  已连接: 'Connected',
  连接失败: 'Connection Failed',
  已断开: 'Disconnected',
  发送: 'Send',
  连接: 'Connect',
  断开连接: 'Disconnect',
  消息历史: 'Message History',
  清空历史: 'Clear History',
  发送消息: 'Send Message',
  请输入消息内容: 'Please enter message content',
  连接地址: 'Connection URL',
  请输入WebSocket连接地址: 'Please enter WebSocket connection URL',
  连接参数: 'Connection Parameters',
  请求头参数: 'Request Header Parameters',
  WebSocket连接测试: 'WebSocket Connection Test',
  点击连接按钮建立WebSocket连接: 'Click connect button to establish WebSocket connection',
  消息内容: 'Message Content',
  项目特色功能视频演示: 'Project Feature Video Demo',
  下载: 'Download',
  个人基本信息: 'Personal Basic Information',
  用户头像: 'User Avatar',
  更换头像: 'Change Avatar',
  所属团队: 'Team',
  编辑信息: 'Edit Information',
  返回上级: 'Back to Parent',
  修改密码: 'Change Password',
  登录名称: 'Login Name',
  原密码: 'Original Password',
  请输入原密码: 'Please enter original password',
  新密码: 'New Password',
  请输入新密码: 'Please enter new password',
  请再次输入新密码: 'Please enter new password again',
  支持正则表达式: 'Supports regex, eg: /pattern/flags or pattern',
  输入关键词筛选: 'Enter keywords to filter message content...',
  切换正则表达式模式: 'Toggle regex mode',
  切换原始数据视图: 'Toggle raw data view',
  下载SSE数据: 'Download SSE data',
  找到: 'Found',
  条匹配结果: 'matching result(s)',
  未找到匹配结果: 'No matching results found',
  下载WebSocket数据: 'Download WebSocket data',
  接收: 'Receive',
  开始连接: 'Start Connect',
  重连中: 'Reconnecting',
  正则表达式错误: 'Regex Error',
  未知错误: 'Unknown Error',
  下载失败: 'Download Failed',
  重试第: 'Retry attempt',
  次URL: ', URL:',
  消息详情: 'Message Details',
  错误信息: 'Error Information',
  内容类型: 'Content Type',
  重连次数: 'Reconnection Count',
  下次重试: 'Next Retry',
  断开原因: 'Disconnect Reason',
  手动断开: 'Manual Disconnect',
  自动断开: 'Auto Disconnect',
  全部消息: 'All Messages',
  心跳: 'Heartbeat',
}
