// 自动生成的CSS清理脚本
// 请在执行前仔细检查每个删除操作

import fs from "fs";
import path from "path";

const filesToClean = [
  {
    file: "src\renderer\components\apidoc\mock\g-mock.vue",
    selectorsToRemove: [
      ".bar",
      ".preview",
    ]
  },
  {
    file: "src\renderer\components\apidoc\params-tree\g-params-tree.vue",
    selectorsToRemove: [
      ".valid-input .ipt-wrap .ipt-inner",
      ".file-error",
      "&.no-border",
      ".mode-list",
    ]
  },
  {
    file: "src\renderer\components\apidoc\params-view\g-params-view.vue",
    selectorsToRemove: [
      "&.error",
      ".indent",
      ".path",
      ".description",
      ".colon",
      ".colon",
      ".bracket",
      ".comma",
      ".curly-brace",
      ".string-value",
      ".boolean-value",
      ".number-value",
      ".null-value",
      ".file-value",
    ]
  },
  {
    file: "src\renderer\components\common\collapse-card\g-collapse-card.vue",
    selectorsToRemove: [
      ".tail",
      "// 内容区域
  .content",
    ]
  },
  {
    file: "src\renderer\components\common\ellipsis-content\g-ellipsis-content.vue",
    selectorsToRemove: [
      ".copy",
    ]
  },
  {
    file: "src\renderer\components\common\sse-view\components\popover\sse-popover.vue",
    selectorsToRemove: [
      ".raw-block",
      ".raw-block",
    ]
  },
  {
    file: "src\renderer\components\common\sse-view\g-sse-view.vue",
    selectorsToRemove: [
      ".action-icons",
      ".icon",
      "font-size: 12px;
          padding: 8px 12px;
          margin: 0;

          &:not(.error):not(.no-result)",
      "&.no-result",
      "&.error",
      "font-size: 12px;

          &:not(.error):not(.no-result)",
      "&.no-result",
      "&.error",
      "#909399);
    font-size: 14px;
    gap: 12px;

    .loading-icon",
      "#ebeef5);
    border-radius: 4px;
    margin: 0 12px 12px 12px;

    .raw-data",
      "#ffffff);

      &.sse-message-hex",
      ".message-index",
      ".message-content",
      ".message-timestamp",
      "#f2f6fc);
        cursor: pointer;

        &.sse-message-hex",
      "// 高亮样式
:deep(.highlight)",
    ]
  },
  {
    file: "src\renderer\components\common\websocket-view\components\filter\websocket-filter.vue",
    selectorsToRemove: [
      "display: flex;
      align-items: center;
      justify-content: flex-end;
      height: 35px;
      flex: 0 0 40%;
      margin-left: auto;
      .message-type-filter",
      ".download-icon:hover",
      ".clear-icon:hover",
      "font-size: 12px;
        padding: 8px 12px;
        margin: 0;

        &:not(.error):not(.no-result)",
      "&.no-result",
      "&.error",
    ]
  },
  {
    file: "src\renderer\components\common\websocket-view\components\popover\websocket-popover.vue",
    selectorsToRemove: [
      "padding: 2px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;

          &.type-send",
      "&.type-receive",
      "&.type-connected",
      "&.type-disconnected",
      "&.type-error",
      "&.type-heartbeat",
      "&.type-startConnect",
      "&.type-reconnecting",
    ]
  },
  {
    file: "src\renderer\components\common\websocket-view\g-websocket-view.vue",
    selectorsToRemove: [
      "border-radius: 3px;
        font-size: 14px;
        min-width: 20px;
        text-align: center;
        margin-right: 10px;
        display: flex;
        align-items: center;
        justify-content: center;

        &.type-send",
      "&.type-heartbeat",
      "&.type-receive",
    ]
  },
  {
    file: "src\renderer\pages\header\header.vue",
    selectorsToRemove: [
      ".add-tab-btn",
      ".add-tab-btn:focus",
      ".add-tab-btn:hover",
      ".right",
      ".navigation-control",
      ".navigation-control i",
      ".navigation-control i:hover",
      ".language-btn",
      ".language-btn:hover",
      ".language-text",
      ".window-control",
      ".window-control i",
      ".window-control i:hover",
    ]
  },
  {
    file: "src\renderer\pages\layout\header.vue",
    selectorsToRemove: [
      "&.close:hover",
      ".process",
    ]
  },
  {
    file: "src\renderer\pages\login\login.vue",
    selectorsToRemove: [
      ".item-wrap",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\banner\banner.vue",
    selectorsToRemove: [
      ".ws-icon",
      ".folder-icon",
      "display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top",
      ".node-bottom",
      "flex: 0 0 75%;
      height: 22px;
      border: 1px solid var(--theme-color);
      font-size: 1em;
      margin-left: -1px;

      &.error",
      ".folder-icon",
      ".folder-icon",
      ".hot-key",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\banner\tool\tool.vue",
    selectorsToRemove: [
      "position: relative;
    align-items: center;
    display: flex;

    .item",
      ".operation",
      ".more",
      "height: 40px;
  width: 100%;
  padding: 0 10px 0 20px;
  display: flex;
  align-items: center;

  // cursor: default;
  .label",
      ".shortcut",
      ".svg-icon",
      ".project-wrap",
      "height: 35px;
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: space-between;

    .item-title",
      "background-color: var(--theme-color);
      color: var(--white);
      cursor: pointer;

      .item-content",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\dialog\curd-host\curd-host.vue",
    selectorsToRemove: [
      ".url-wrap",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\operation\operation.vue",
    selectorsToRemove: [
      "position: sticky;
  top: 0;
  padding: 10px 20px;
  box-shadow: 0 3px 2px var(--gray-400);
  background: var(--white);
  z-index: var(--zIndex-request-info-wrap);
  height: var(--apiflow-apidoc-operation-height);

  &.prefix",
      ".proxy-wrap",
      ".tip",
      ".env-item",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\body\body.vue",
    selectorsToRemove: [
      ".operation",
      ".head",
      ".tail",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\headers\headers.vue",
    selectorsToRemove: [
      ".value-wrap",
      ".folder-icon",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\mock\components\mock-response\mock-response.vue",
    selectorsToRemove: [
      "height: calc(100vh - 610px);
    min-height: 200px;
    border: 1px solid var(--gray-500);
    display: flex;
    position: relative;

    .mock-json-editor",
      ".tip",
      ".format-btn",
      ".raw-editor-wrap",
      ".img-wrap",
      ".image-demo",
      ".svg-icon",
      ".img",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\params.vue",
    selectorsToRemove: [
      ".workbench",
      "transition: none;
    top: 10px;

    &.is-fixed.is-dot",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\params\response\response.vue",
    selectorsToRemove: [
      "max-width: 200px;

      .type-text",
      ".editor",
      ".format-btn",
      ".header",
      ".tail",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\base-info\base-info.vue",
    selectorsToRemove: [
      "flex-grow: 0;
  flex-shrink: 0;
  box-shadow: 0 3px 2px var(--gray-400);
  margin-bottom: 10px;
  padding: 10px;
  height: var(--apiflow-apidoc-request-view-height);
  overflow: hidden;

  .svg-icon",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\body\body.vue",
    selectorsToRemove: [
      ".apply-response",
      "height: 100%;
    .text-tool",
      ".operation",
      ".process",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\request\request.vue",
    selectorsToRemove: [
      ".body-wrap",
      "margin-left: 25px;
    .download",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\response\res-info\res-info.vue",
    selectorsToRemove: [
      "overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

  .content-type",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apidoc\view\view.vue",
    selectorsToRemove: [
      ".url",
      ".view-block",
      ".title",
      ".remark",
      ".api-doc-subtitle",
      ".api-doc-method",
      ".api-doc-url",
      ".api-doc-description",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\apiflow\components\node\node.vue",
    selectorsToRemove: [
      ".create-line-dot",
      ".resize-border",
      ".position-info",
      "0.12);
        .title",
      "height: 30px;
        display: flex;
        align-items: center;
        font-size: 13px;
        padding: 0 10px;
        width: 100%;
        .method",
      ".url",
      ".content",
      ".empty",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\config\config.vue",
    selectorsToRemove: [
      "height: 40px;
      display: flex;
      align-items: center;
      position: relative;
      font-size: 12px;
      flex: 0 0 auto;
      width: 200px;
      cursor: default;
      padding: 0 10px;
      .item-text",
      ".iconfont",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\cookies\cookies.vue",
    selectorsToRemove: [
      "margin: 0 auto;
  padding: 16px 0;
  font-size: 22px;
  .expire-tip",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\export\export.vue",
    selectorsToRemove: [
      ".more",
      ".folder-icon",
      "display: flex;
                flex-direction: column;
                flex: 1;
                overflow: hidden;
                .node-top",
      ".node-bottom",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\export\fork\fork.vue",
    selectorsToRemove: [
      ".right",
      ".more",
      ".folder-icon",
      "display: flex;
        flex-direction: column;
        flex: 1;
        overflow: hidden;

        .node-top",
      ".node-bottom",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\import\import.vue",
    selectorsToRemove: [
      ".more",
      ".file-icon",
      ".folder-icon",
      "display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top",
      ".node-bottom",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\add.vue",
    selectorsToRemove: [
      ".link-icon",
      ".more",
      ".folder-icon",
      "display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .node-top",
      ".node-bottom",
      "flex: 0 0 75%;
      height: 22px;
      border: 1px solid #409EFF;
      font-size: 1em;
      margin-left: -1px;
      &.error",
      ".more",
      "&.select-node",
      ".folder-icon",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\link\dialog\edit.vue",
    selectorsToRemove: [
      ".link-icon",
      ".more",
      ".folder-icon",
      "display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;
      .node-top",
      ".node-bottom",
      "flex: 0 0 75%;
      height: 22px;
      border: 1px solid #409EFF;
      font-size: 1em;
      margin-left: -1px;
      &.error",
      ".more",
      "&.select-node",
      ".folder-icon",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\link\link.vue",
    selectorsToRemove: [
      "// 内容区域
  .content-area",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\package\package.vue",
    selectorsToRemove: [
      "display: flex;
    height: calc(100vh - 100px);
    padding: 20px;
    overflow-y: auto;
    .drag-wrap",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\recycler\recycler.vue",
    selectorsToRemove: [
      ".desc",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\variable\variable.vue",
    selectorsToRemove: [
      "flex: 0 0 500px;
    margin-right: 10px;
    .file-notice",
      ".right",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\websocket\operation\operation.vue",
    selectorsToRemove: [
      ".action-buttons",
      ".status-tag",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\headers\headers.vue",
    selectorsToRemove: [
      ".value-wrap",
      ".folder-icon",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\message\message.vue",
    selectorsToRemove: [
      ".action-options",
      "display: flex;
          align-items: center;
          margin-left: 24px;

          .interval-unit",
      "margin-bottom: 16px;

    .config-label",
      "display: flex;
      align-items: center;
      gap: 8px;

      .interval-unit",
      ".heartbeat-content-input",
      ".config-actions",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\params.vue",
    selectorsToRemove: [
      ".workbench",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\content\websocket\params\pre-script\pre-script.vue",
    selectorsToRemove: [
      ".script-actions",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\dialog\save-doc\save-doc.vue",
    selectorsToRemove: [
      ".more",
      ".file-icon",
      ".folder-icon",
      "display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top",
      ".node-bottom",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-edit\nav\nav.vue",
    selectorsToRemove: [
      "display: inline-block;
        width: 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        // font-size: fz(12);
        &.unfixed",
      ".iconfont",
      "position: absolute;
      right: 0;
      width: 25px;
      height: 100%;
      cursor: pointer;

      &:hover>.has-change",
      "&:hover>.close",
      "-50%);
        width: 20px;
        height: 20px;
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        z-index: 2;

        .dot",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-list\tab-a\tab-a.vue",
    selectorsToRemove: [
      ".advance-icon",
      ".project-bottom",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-list\tab-b\tab-b.vue",
    selectorsToRemove: [
      ".user-list",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-share\banner\banner.vue",
    selectorsToRemove: [
      ".folder-icon",
      "display: flex;
      flex-direction: column;
      flex: 1;
      overflow: hidden;

      .node-top",
      ".node-bottom",
      "&.select-node",
      ".folder-icon",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-share\content\content.vue",
    selectorsToRemove: [
      ".content-format-label",
      ".api-doc-subtitle",
      ".api-doc-empty",
      "display: flex;
    align-items: center;
    gap: 16px;
    margin-bottom: 8px;
    
    .api-doc-response-title",
      ".status-code.success",
      ".type-label",
      "// 原始数据样式
.api-doc-raw-body",
      ".api-doc-response-meta",
    ]
  },
  {
    file: "src\renderer\pages\modules\apidoc\doc-share\nav\nav.vue",
    selectorsToRemove: [
      "display: inline-block;
        width: 130px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.unfixed",
    ]
  },
  {
    file: "src\renderer\pages\modules\permission\menu\menu.vue",
    selectorsToRemove: [
      "width: 100%;
    height: 30px;
    display: flex;
    align-items: center;
    overflow: hidden;

    .label",
      ".contextmenu",
    ]
  },
  {
    file: "src\renderer\pages\modules\permission\role\add\components\client-menus.vue",
    selectorsToRemove: [
      "display: flex;
      align-items: center;
      height: 30px;
      width: 100%;

      .node-name",
    ]
  },
  {
    file: "src\renderer\pages\modules\permission\role\edit\components\client-menus.vue",
    selectorsToRemove: [
      "display: flex;
      align-items: center;
      height: 30px;
      width: 100%;

      .node-name",
    ]
  },
  {
    file: "src\renderer\pages\modules\user-center\cacheManager\components\DataBackup.vue",
    selectorsToRemove: [
      "display: inline-block;
    padding: 8px 12px;
    background: var(--bg-light);
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    
    .info-text",
      ".path-error-message",
      ".progress-info",
    ]
  },
  {
    file: "src\renderer\pages\modules\user-center\cacheManager\components\IndexedDBDetail.vue",
    selectorsToRemove: [
      "0.1);
    padding: 40px 20px;
    text-align: center;

    .empty-text",
    ]
  },
  {
    file: "src\renderer\pages\modules\user-center\cacheManager\components\LocalStorageDetail.vue",
    selectorsToRemove: [
      "0.1);
    padding: 40px 20px;
    text-align: center;

    .empty-text",
    ]
  },
  {
    file: "src\renderer\pages\modules\user-center\cacheManager\dialog\indexedDBDialog.vue",
    selectorsToRemove: [
      ".value-content",
      ".value-preview",
      ".value-preview.clickable",
      ".value-preview.clickable:hover",
      ".json-popover-content",
      ".popover-header",
      ".popover-title",
      ".json-editor-container",
      ".pagination-container",
      ".empty-detail",
      ".empty-text",
    ]
  },
  {
    file: "src\renderer\pages\modules\user-center\UserCenter.vue",
    selectorsToRemove: [
      ".fade-enter-from",
      ".fade-leave-to",
    ]
  },
];

// 执行清理的函数
function cleanupCSS() {
  console.log("开始清理CSS...");
  // 实现清理逻辑
}