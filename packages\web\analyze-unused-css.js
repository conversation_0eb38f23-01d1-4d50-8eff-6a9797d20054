import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// CSS选择器类型
const CSS_SELECTOR_TYPES = {
  CLASS: 'class',
  ID: 'id',
  TAG: 'tag',
  ATTRIBUTE: 'attribute',
  PSEUDO: 'pseudo'
};

// 解析CSS选择器
function parseCSSSelectors(cssContent) {
  const selectors = [];
  
  // 移除注释
  cssContent = cssContent.replace(/\/\*[\s\S]*?\*\//g, '');
  
  // 匹配CSS规则
  const ruleRegex = /([^{}]+)\s*\{[^{}]*\}/g;
  let match;
  
  while ((match = ruleRegex.exec(cssContent)) !== null) {
    const selectorText = match[1].trim();
    
    // 跳过@规则（如@media, @keyframes等）
    if (selectorText.startsWith('@')) {
      continue;
    }
    
    // 分割多个选择器（逗号分隔）
    const individualSelectors = selectorText.split(',').map(s => s.trim());
    
    individualSelectors.forEach(selector => {
      if (selector) {
        const parsedSelector = parseIndividualSelector(selector);
        if (parsedSelector) {
          selectors.push({
            original: selector,
            ...parsedSelector,
            fullRule: match[0]
          });
        }
      }
    });
  }
  
  return selectors;
}

// 解析单个选择器
function parseIndividualSelector(selector) {
  // 类选择器
  const classMatches = selector.match(/\.([a-zA-Z_-][a-zA-Z0-9_-]*)/g);
  if (classMatches) {
    return {
      type: CSS_SELECTOR_TYPES.CLASS,
      names: classMatches.map(c => c.substring(1)) // 移除点号
    };
  }
  
  // ID选择器
  const idMatches = selector.match(/#([a-zA-Z_-][a-zA-Z0-9_-]*)/g);
  if (idMatches) {
    return {
      type: CSS_SELECTOR_TYPES.ID,
      names: idMatches.map(i => i.substring(1)) // 移除#号
    };
  }
  
  // 标签选择器（简单处理）
  const tagMatch = selector.match(/^([a-zA-Z][a-zA-Z0-9]*)/);
  if (tagMatch && !selector.includes('.') && !selector.includes('#') && !selector.includes('[')) {
    return {
      type: CSS_SELECTOR_TYPES.TAG,
      names: [tagMatch[1]]
    };
  }
  
  return null;
}

// 解析Vue文件
function parseVueFile(filePath) {
  const content = fs.readFileSync(filePath, 'utf-8');
  
  // 提取template部分
  const templateMatch = content.match(/<template[^>]*>([\s\S]*?)<\/template>/);
  const templateContent = templateMatch ? templateMatch[1] : '';
  
  // 提取style部分
  const styleMatches = content.matchAll(/<style[^>]*>([\s\S]*?)<\/style>/g);
  let styleContent = '';
  for (const match of styleMatches) {
    styleContent += match[1] + '\n';
  }
  
  return {
    template: templateContent,
    style: styleContent
  };
}

// 检查选择器是否在模板中使用
function isUsedInTemplate(selector, templateContent) {
  if (!selector.names || selector.names.length === 0) {
    return true; // 保守处理，如果无法解析则认为被使用
  }
  
  for (const name of selector.names) {
    if (selector.type === CSS_SELECTOR_TYPES.CLASS) {
      // 检查静态类名
      if (templateContent.includes(`class="${name}"`) ||
          templateContent.includes(`class='${name}'`) ||
          templateContent.includes(`class=".*${name}.*"`) ||
          templateContent.includes(`class='.*${name}.*'`)) {
        return true;
      }
      
      // 检查动态类名绑定
      if (templateContent.includes(`:class`) || templateContent.includes(`v-bind:class`)) {
        // 简单检查是否在动态绑定中提到了这个类名
        if (templateContent.includes(`'${name}'`) || templateContent.includes(`"${name}"`)) {
          return true;
        }
      }
      
      // 检查模板中是否包含类名字符串（可能在计算属性或方法中使用）
      if (templateContent.includes(name)) {
        return true;
      }
    } else if (selector.type === CSS_SELECTOR_TYPES.ID) {
      if (templateContent.includes(`id="${name}"`) || 
          templateContent.includes(`id='${name}'`) ||
          templateContent.includes(`#${name}`)) {
        return true;
      }
    } else if (selector.type === CSS_SELECTOR_TYPES.TAG) {
      if (templateContent.includes(`<${name}`) || templateContent.includes(`</${name}>`)) {
        return true;
      }
    }
  }
  
  return false;
}

// 分析单个Vue文件
function analyzeVueFile(filePath) {
  try {
    const { template, style } = parseVueFile(filePath);
    
    if (!style.trim()) {
      return { file: filePath, unusedSelectors: [] };
    }
    
    const selectors = parseCSSSelectors(style);
    const unusedSelectors = [];
    
    for (const selector of selectors) {
      if (!isUsedInTemplate(selector, template)) {
        unusedSelectors.push(selector);
      }
    }
    
    return {
      file: filePath,
      unusedSelectors,
      totalSelectors: selectors.length
    };
  } catch (error) {
    console.error(`Error analyzing ${filePath}:`, error.message);
    return { file: filePath, unusedSelectors: [], error: error.message };
  }
}

// 获取所有Vue文件
function getAllVueFiles(dir) {
  const vueFiles = [];
  
  function scanDirectory(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
        scanDirectory(fullPath);
      } else if (stat.isFile() && item.endsWith('.vue')) {
        vueFiles.push(fullPath);
      }
    }
  }
  
  scanDirectory(dir);
  return vueFiles;
}

// 主分析函数
function analyzeProject(srcDir) {
  console.log('开始分析Vue文件中的未使用CSS样式...');
  
  const vueFiles = getAllVueFiles(srcDir);
  console.log(`找到 ${vueFiles.length} 个Vue文件`);
  
  const results = [];
  let totalUnusedSelectors = 0;
  
  for (let i = 0; i < vueFiles.length; i++) {
    const file = vueFiles[i];
    const relativePath = path.relative(srcDir, file);
    
    process.stdout.write(`\r分析进度: ${i + 1}/${vueFiles.length} - ${relativePath}`);
    
    const result = analyzeVueFile(file);
    if (result.unusedSelectors.length > 0) {
      results.push(result);
      totalUnusedSelectors += result.unusedSelectors.length;
    }
  }
  
  console.log('\n\n分析完成！');
  console.log(`发现 ${totalUnusedSelectors} 个可能未使用的CSS选择器`);
  
  return results;
}

// 生成报告
function generateReport(results) {
  console.log('\n=== 未使用CSS样式报告 ===\n');
  
  if (results.length === 0) {
    console.log('🎉 没有发现未使用的CSS样式！');
    return;
  }
  
  results.forEach((result, index) => {
    const relativePath = path.relative(process.cwd(), result.file);
    console.log(`${index + 1}. 文件: ${relativePath}`);
    console.log(`   总选择器数: ${result.totalSelectors || 0}`);
    console.log(`   未使用选择器数: ${result.unusedSelectors.length}`);
    
    result.unusedSelectors.forEach((selector, sIndex) => {
      console.log(`   ${sIndex + 1}) ${selector.original} (${selector.type})`);
      if (selector.names) {
        console.log(`       名称: ${selector.names.join(', ')}`);
      }
    });
    console.log('');
  });
}

// 运行分析
const srcDir = path.join(__dirname, 'src');
const results = analyzeProject(srcDir);
generateReport(results);

// 保存结果到JSON文件
fs.writeFileSync('unused-css-report.json', JSON.stringify(results, null, 2));
console.log('详细报告已保存到 unused-css-report.json');

export { analyzeProject, generateReport };
