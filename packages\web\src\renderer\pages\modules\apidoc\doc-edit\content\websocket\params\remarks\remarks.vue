<template>
  <div class="remarks">
    <div class="remarks-editor">
      <el-input 
        v-model="remarks" 
        type="textarea" 
        :rows="15"
        placeholder="请输入WebSocket接口的备注信息..."
        autocomplete="off"
        autocorrect="off"
        spellcheck="false"
      />
    </div>
    
    <div class="remarks-actions">
      <el-button type="primary" @click="handleSaveRemarks">
        {{ t("保存备注") }}
      </el-button>
      <el-button @click="handleClearRemarks">
        {{ t("清空备注") }}
      </el-button>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { useTranslation } from 'i18next-vue'
import { useWebSocket } from '@/store/websocket/websocket'

const { t } = useTranslation()
const websocketStore = useWebSocket()

const remarks = computed({
  get: () => websocketStore.websocket.info.description,
  set: (value: string) => websocketStore.changeWebSocketDescription(value)
})

const handleSaveRemarks = () => {
  console.log('保存备注信息:', remarks.value)
}

const handleClearRemarks = () => {
  websocketStore.changeWebSocketDescription('')
}
</script>

<style lang="scss" scoped>
.remarks {
  padding: 16px;
  height: 100%;
  display: flex;
  flex-direction: column;

  .remarks-editor {
    flex: 1;
    margin-bottom: 16px;
  }

  .remarks-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-start;
  }
}
</style>
