import fs from 'fs';
import path from 'path';

// 读取分析结果
const results = JSON.parse(fs.readFileSync('unused-css-report.json', 'utf-8'));

// 分类未使用的样式
function categorizeUnusedStyles(results) {
  const categories = {
    // 可能是Element Plus相关的样式（谨慎删除）
    elementPlus: [],
    // 明显的自定义类名（可以安全删除）
    customClasses: [],
    // ID选择器
    idSelectors: [],
    // 标签选择器（通常不应删除）
    tagSelectors: [],
    // 可能的动态类名（需要手动检查）
    possibleDynamic: []
  };

  results.forEach(fileResult => {
    fileResult.unusedSelectors.forEach(selector => {
      const item = {
        file: fileResult.file,
        selector: selector.original,
        type: selector.type,
        names: selector.names,
        fullRule: selector.fullRule
      };

      // 分类逻辑
      if (selector.names && selector.names.some(name => name.startsWith('el-'))) {
        categories.elementPlus.push(item);
      } else if (selector.type === 'id') {
        categories.idSelectors.push(item);
      } else if (selector.type === 'tag') {
        categories.tagSelectors.push(item);
      } else if (selector.names && selector.names.some(name => 
        name.includes('active') || 
        name.includes('hover') || 
        name.includes('focus') || 
        name.includes('disabled') ||
        name.includes('selected') ||
        name.includes('current')
      )) {
        categories.possibleDynamic.push(item);
      } else {
        categories.customClasses.push(item);
      }
    });
  });

  return categories;
}

// 生成清理建议
function generateCleanupSuggestions(categories) {
  const suggestions = [];

  // 统计信息
  const totalUnused = Object.values(categories).reduce((sum, cat) => sum + cat.length, 0);
  
  suggestions.push('=== CSS样式清理分析报告 ===\n');
  suggestions.push(`总计发现 ${totalUnused} 个可能未使用的CSS选择器\n`);

  // 1. 安全删除的自定义类名
  if (categories.customClasses.length > 0) {
    suggestions.push('🟢 【建议删除】自定义类名 (相对安全):');
    suggestions.push(`共 ${categories.customClasses.length} 个\n`);
    
    const fileGroups = {};
    categories.customClasses.forEach(item => {
      const relativePath = path.relative(process.cwd(), item.file);
      if (!fileGroups[relativePath]) {
        fileGroups[relativePath] = [];
      }
      fileGroups[relativePath].push(item);
    });

    Object.entries(fileGroups).forEach(([file, items]) => {
      suggestions.push(`📁 ${file}:`);
      items.forEach(item => {
        suggestions.push(`   - .${item.names.join(', .')} (${item.selector})`);
      });
      suggestions.push('');
    });
  }

  // 2. ID选择器
  if (categories.idSelectors.length > 0) {
    suggestions.push('🟡 【需要检查】ID选择器:');
    suggestions.push(`共 ${categories.idSelectors.length} 个\n`);
    
    categories.idSelectors.forEach(item => {
      const relativePath = path.relative(process.cwd(), item.file);
      suggestions.push(`📁 ${relativePath}: #${item.names.join(', #')}`);
    });
    suggestions.push('');
  }

  // 3. 可能的动态类名
  if (categories.possibleDynamic.length > 0) {
    suggestions.push('🟡 【需要手动检查】可能的动态类名:');
    suggestions.push(`共 ${categories.possibleDynamic.length} 个\n`);
    
    categories.possibleDynamic.forEach(item => {
      const relativePath = path.relative(process.cwd(), item.file);
      suggestions.push(`📁 ${relativePath}: .${item.names.join(', .')} (${item.selector})`);
    });
    suggestions.push('');
  }

  // 4. Element Plus相关样式
  if (categories.elementPlus.length > 0) {
    suggestions.push('🔴 【谨慎删除】Element Plus相关样式:');
    suggestions.push(`共 ${categories.elementPlus.length} 个`);
    suggestions.push('这些样式可能用于覆盖Element Plus组件样式，删除前请仔细确认\n');
    
    categories.elementPlus.forEach(item => {
      const relativePath = path.relative(process.cwd(), item.file);
      suggestions.push(`📁 ${relativePath}: ${item.selector}`);
    });
    suggestions.push('');
  }

  // 5. 标签选择器
  if (categories.tagSelectors.length > 0) {
    suggestions.push('🔴 【不建议删除】标签选择器:');
    suggestions.push(`共 ${categories.tagSelectors.length} 个`);
    suggestions.push('标签选择器通常用于全局样式，建议保留\n');
  }

  return suggestions;
}

// 生成删除脚本
function generateCleanupScript(categories) {
  const script = [];
  
  script.push('// 自动生成的CSS清理脚本');
  script.push('// 请在执行前仔细检查每个删除操作');
  script.push('');
  script.push('import fs from "fs";');
  script.push('import path from "path";');
  script.push('');
  script.push('const filesToClean = [');

  // 按文件分组
  const fileGroups = {};
  categories.customClasses.forEach(item => {
    if (!fileGroups[item.file]) {
      fileGroups[item.file] = [];
    }
    fileGroups[item.file].push(item);
  });

  Object.entries(fileGroups).forEach(([file, items]) => {
    const relativePath = path.relative(process.cwd(), file);
    script.push(`  {`);
    script.push(`    file: "${relativePath}",`);
    script.push(`    selectorsToRemove: [`);
    items.forEach(item => {
      script.push(`      "${item.selector}",`);
    });
    script.push(`    ]`);
    script.push(`  },`);
  });

  script.push('];');
  script.push('');
  script.push('// 执行清理的函数');
  script.push('function cleanupCSS() {');
  script.push('  console.log("开始清理CSS...");');
  script.push('  // 实现清理逻辑');
  script.push('}');

  return script;
}

// 主函数
function main() {
  console.log('正在生成清理报告...');
  
  const categories = categorizeUnusedStyles(results);
  const suggestions = generateCleanupSuggestions(categories);
  const cleanupScript = generateCleanupScript(categories);

  // 保存报告
  fs.writeFileSync('css-cleanup-report.txt', suggestions.join('\n'));
  fs.writeFileSync('css-cleanup-script.js', cleanupScript.join('\n'));

  console.log('报告生成完成！');
  console.log('- css-cleanup-report.txt: 详细的清理建议');
  console.log('- css-cleanup-script.js: 自动清理脚本模板');
  
  // 输出摘要
  console.log('\n=== 摘要 ===');
  console.log(`🟢 建议删除的自定义类名: ${categories.customClasses.length} 个`);
  console.log(`🟡 需要检查的ID选择器: ${categories.idSelectors.length} 个`);
  console.log(`🟡 可能的动态类名: ${categories.possibleDynamic.length} 个`);
  console.log(`🔴 Element Plus相关样式: ${categories.elementPlus.length} 个`);
  console.log(`🔴 标签选择器: ${categories.tagSelectors.length} 个`);
}

main();
